// Annotate this macro before synthesis
// `define RUN_TRACE

// TODO: 在此处定义你的宏
`define OP_LUI      7'b0110111  
`define OP_JAL      7'b1101111  
`define OP_JALR     7'b1100111  
`define OP_BRANCH   7'b1100011  
`define OP_LOAD     7'b0000011  
`define OP_STORE    7'b0100011  
`define OP_IMM      7'b0010011  
`define OP_REG      7'b0110011  

// ALU操作码定义
`define ALU_ADD     4'b0000     
`define ALU_SUB     4'b0001    
`define ALU_AND     4'b0010     
`define ALU_OR      4'b0011     
`define ALU_XOR     4'b0100     
`define ALU_SLL     4'b0101    
`define ALU_SRL     4'b0110     
`define ALU_SRA     4'b0111     
`define ALU_SLT     4'b1000     
`define ALU_SLTU    4'b1001    
`define ALU_BEQ     4'b1010     
`define ALU_BLT     4'b1011    

// 立即数类型定义
`define EXT_I       3'b000      
`define EXT_S       3'b001      
`define EXT_B       3'b010     
`define EXT_U       3'b011      
`define EXT_J       3'b100      
`define EXT_shift   3'b101      

// NPC操作类型定义
`define NPC_PC4     2'b00       
`define NPC_PCIMM   2'b01       
`define NPC_RD1IMM  2'b10      

// 写回数据选择定义
`define WB_ALU      2'b00       
`define WB_DRAM     2'b01       
`define WB_PC4      2'b10       
`define WB_EXT      2'b11     

// ALU第二个操作数选择定义
`define ALUB_RS2    1'b0       
`define ALUB_EXT    1'b1    

// 外设I/O接口电路的端口地址
`define PERI_ADDR_DIG   32'hFFFF_F000 
`define PERI_ADDR_TIM0  32'hFFFF_F020  
`define PERI_ADDR_TIM1  32'hFFFF_F024  
`define PERI_ADDR_LED   32'hFFFF_F060  
`define PERI_ADDR_SW    32'hFFFF_F070  
`define PERI_ADDR_BTN   32'hFFFF_F078  
