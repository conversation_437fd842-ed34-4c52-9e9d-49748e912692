#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Fri Jul  4 11:00:44 2025
# Process ID: 32044
# Current directory: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent19208 C:\Users\<USER>\Desktop\实验\cpu设计\lab2\proj_miniRV_ego1\proj_single_cycle\proj_single_cycle.xpr
# Log file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/vivado.log
# Journal file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle\vivado.jou
#-----------------------------------------------------------
start_gui
open_project C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.xpr
Scanning sources...
Finished scanning sources
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'E:/Vivado/2018.3/data/ip'.
update_compile_order -fileset sources_1
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
[Fri Jul  4 11:31:48 2025] Launched synth_1...
Run output will be captured here: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/runme.log
[Fri Jul  4 11:31:48 2025] Launched impl_1...
Run output will be captured here: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/runme.log
open_hw
connect_hw_server
INFO: [Labtools 27-2285] Connecting to hw_server url TCP:localhost:3121
INFO: [Labtools 27-2222] Launching hw_server...
INFO: [Common 17-41] Interrupt caught. Command should exit soon.
INFO: [Labtools 27-2221] Launch Output:

****** Xilinx hw_server v2018.3
  **** Build date : Dec  7 2018-00:40:27
    ** Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.


INFO: [Labtools 27-2058] connect_hw_server command cancelled.
INFO: [Common 17-344] 'connect_hw_server' was cancelled
exit
INFO: [Common 17-206] Exiting Vivado at Fri Jul  4 11:52:16 2025...
