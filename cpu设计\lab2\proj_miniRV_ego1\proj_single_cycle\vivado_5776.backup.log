#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Thu Jul  3 13:48:40 2025
# Process ID: 5776
# Current directory: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent16252 C:\Users\<USER>\Desktop\实验\cpu设计\lab2\proj_miniRV_ego1\proj_single_cycle\proj_single_cycle.xpr
# Log file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/vivado.log
# Journal file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle\vivado.jou
#-----------------------------------------------------------
start_gui
open_project C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.xpr
INFO: [Project 1-313] Project file moved from 'Y:/proj_miniRV/proj_single_cycle' since last save.
Scanning sources...
Finished scanning sources
WARNING: [filemgmt 56-2] IPUserFilesDir: Could not find the directory 'C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files', nor could it be found using path 'Y:/proj_miniRV/proj_single_cycle/proj_single_cycle.ip_user_files'.
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'E:/Vivado/2018.3/data/ip'.
update_compile_order -fileset sources_1
exit
INFO: [Common 17-206] Exiting Vivado at Thu Jul  3 13:56:45 2025...
