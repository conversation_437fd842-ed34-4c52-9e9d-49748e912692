#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Thu Jul  3 17:11:53 2025
# Process ID: 13676
# Current directory: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1
# Command line: vivado.exe -log miniRV_SoC.vdi -applog -product Vivado -messageDb vivado.pb -mode batch -source miniRV_SoC.tcl -notrace
# Log file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC.vdi
# Journal file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1\vivado.jou
#-----------------------------------------------------------
source miniRV_SoC.tcl -notrace
Command: link_design -top miniRV_SoC -part xc7a35tcsg324-1
Design is defaulting to srcset: sources_1
Design is defaulting to constrset: constrs_1
INFO: [Project 1-454] Reading design checkpoint 'c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.dcp' for cell 'Clkgen'
INFO: [Project 1-454] Reading design checkpoint 'c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.dcp' for cell 'Mem_DRAM'
INFO: [Project 1-454] Reading design checkpoint 'c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.dcp' for cell 'Mem_IROM'
INFO: [Netlist 29-17] Analyzing 2555 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 1 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2018.3
INFO: [Device 21-403] Loading part xc7a35tcsg324-1
INFO: [Project 1-570] Preparing netlist for logic optimization
Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_board.xdc] for cell 'Clkgen/inst'
Finished Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_board.xdc] for cell 'Clkgen/inst'
Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc] for cell 'Clkgen/inst'
INFO: [Timing 38-35] Done setting XDC timing constraints. [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc:57]
INFO: [Timing 38-2] Deriving generated clocks [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc:57]
Finished Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc] for cell 'Clkgen/inst'
Parsing XDC File [C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/constrs_1/new/miniRV_clock.xdc]
WARNING: [Constraints 18-619] A clock with name 'fpga_clk' already exists, overwriting the previous clock with the same name. [C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/constrs_1/new/miniRV_clock.xdc:2]
Finished Parsing XDC File [C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/constrs_1/new/miniRV_clock.xdc]
Parsing XDC File [C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/constrs_1/new/miniRV_SoC.xdc]
Finished Parsing XDC File [C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/constrs_1/new/miniRV_SoC.xdc]
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1292.875 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
  A total of 2060 instances were transformed.
  RAM256X1S => RAM256X1S (MUXF7, MUXF7, MUXF8, RAMS64E, RAMS64E, RAMS64E, RAMS64E): 2048 instances
  RAM32M => RAM32M (RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMS32, RAMS32): 12 instances

12 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
link_design completed successfully
link_design: Time (s): cpu = 00:00:09 ; elapsed = 00:00:10 . Memory (MB): peak = 1292.875 ; gain = 934.539
Command: opt_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a35t'
Running DRC as a precondition to command opt_design

Starting DRC Task
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Project 1-461] DRC finished with 0 Errors
INFO: [Project 1-462] Please refer to the DRC report (report_drc) for more information.

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.302 . Memory (MB): peak = 1292.875 ; gain = 0.000

Starting Cache Timing Information Task
INFO: [Timing 38-35] Done setting XDC timing constraints.
Ending Cache Timing Information Task | Checksum: 1a7b83f58

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.330 . Memory (MB): peak = 1305.977 ; gain = 13.102

Starting Logic Optimization Task

Phase 1 Retarget
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
INFO: [Opt 31-49] Retargeted 0 cell(s).
Phase 1 Retarget | Checksum: 1fc0d91ad

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.468 . Memory (MB): peak = 1387.809 ; gain = 0.000
INFO: [Opt 31-389] Phase Retarget created 8 cells and removed 10 cells
INFO: [Opt 31-1021] In phase Retarget, 1 netlist objects are constrained preventing optimization. Please run opt_design with -debug_log to get more detail. 

Phase 2 Constant propagation
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Phase 2 Constant propagation | Checksum: 243dfa216

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.500 . Memory (MB): peak = 1387.809 ; gain = 0.000
INFO: [Opt 31-389] Phase Constant propagation created 0 cells and removed 0 cells

Phase 3 Sweep
Phase 3 Sweep | Checksum: 1ed49631b

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.561 . Memory (MB): peak = 1387.809 ; gain = 0.000
INFO: [Opt 31-389] Phase Sweep created 0 cells and removed 32 cells

Phase 4 BUFG optimization
INFO: [Opt 31-194] Inserted BUFG Clkgen/inst/clk_out1_cpuclk_BUFG_inst to drive 0 load(s) on clock net Clkgen/inst/clk_out1_cpuclk_BUFG
INFO: [Opt 31-193] Inserted 1 BUFG(s) on clock nets
Phase 4 BUFG optimization | Checksum: 2917bcc4e

Time (s): cpu = 00:00:02 ; elapsed = 00:00:00.998 . Memory (MB): peak = 1387.809 ; gain = 0.000
INFO: [Opt 31-662] Phase BUFG optimization created 0 cells of which 0 are BUFGs and removed 0 cells.

Phase 5 Shift Register Optimization
Phase 5 Shift Register Optimization | Checksum: 137b6c6f2

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1387.809 ; gain = 0.000
INFO: [Opt 31-389] Phase Shift Register Optimization created 0 cells and removed 0 cells

Phase 6 Post Processing Netlist
Phase 6 Post Processing Netlist | Checksum: 1d1668a17

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1387.809 ; gain = 0.000
INFO: [Opt 31-389] Phase Post Processing Netlist created 0 cells and removed 0 cells
Opt_design Change Summary
=========================


-------------------------------------------------------------------------------------------------------------------------
|  Phase                        |  #Cells created  |  #Cells Removed  |  #Constrained objects preventing optimizations  |
-------------------------------------------------------------------------------------------------------------------------
|  Retarget                     |               8  |              10  |                                              1  |
|  Constant propagation         |               0  |               0  |                                              0  |
|  Sweep                        |               0  |              32  |                                              0  |
|  BUFG optimization            |               0  |               0  |                                              0  |
|  Shift Register Optimization  |               0  |               0  |                                              0  |
|  Post Processing Netlist      |               0  |               0  |                                              0  |
-------------------------------------------------------------------------------------------------------------------------



Starting Connectivity Check Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1387.809 ; gain = 0.000
Ending Logic Optimization Task | Checksum: 1f390e235

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1387.809 ; gain = 0.000

Starting Power Optimization Task
INFO: [Pwropt 34-132] Skipping clock gating for clocks with a period < 2.00 ns.
Ending Power Optimization Task | Checksum: 1f390e235

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.012 . Memory (MB): peak = 1387.809 ; gain = 0.000

Starting Final Cleanup Task
Ending Final Cleanup Task | Checksum: 1f390e235

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1387.809 ; gain = 0.000

Starting Netlist Obfuscation Task
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1387.809 ; gain = 0.000
Ending Netlist Obfuscation Task | Checksum: 1f390e235

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1387.809 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
31 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
opt_design completed successfully
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1387.809 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.015 . Memory (MB): peak = 1387.809 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1387.809 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC_opt.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file miniRV_SoC_drc_opted.rpt -pb miniRV_SoC_drc_opted.pb -rpx miniRV_SoC_drc_opted.rpx
Command: report_drc -file miniRV_SoC_drc_opted.rpt -pb miniRV_SoC_drc_opted.pb -rpx miniRV_SoC_drc_opted.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC_drc_opted.rpt.
report_drc completed successfully
Command: place_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a35t'
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.
Running DRC as a precondition to command place_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.

Starting Placer Task
INFO: [Place 30-611] Multithreading enabled for place_design using a maximum of 2 CPUs

Phase 1 Placer Initialization

Phase 1.1 Placer Initialization Netlist Sorting
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1387.809 ; gain = 0.000
Phase 1.1 Placer Initialization Netlist Sorting | Checksum: 135d91528

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.009 . Memory (MB): peak = 1387.809 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1387.809 ; gain = 0.000

Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device
INFO: [Timing 38-35] Done setting XDC timing constraints.
Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device | Checksum: 18bb62e00

Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 1387.809 ; gain = 0.000

Phase 1.3 Build Placer Netlist Model
Phase 1.3 Build Placer Netlist Model | Checksum: 2081e48a5

Time (s): cpu = 00:00:05 ; elapsed = 00:00:04 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 1.4 Constrain Clocks/Macros
Phase 1.4 Constrain Clocks/Macros | Checksum: 2081e48a5

Time (s): cpu = 00:00:05 ; elapsed = 00:00:04 . Memory (MB): peak = 1530.242 ; gain = 142.434
Phase 1 Placer Initialization | Checksum: 2081e48a5

Time (s): cpu = 00:00:05 ; elapsed = 00:00:04 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 2 Global Placement

Phase 2.1 Floorplanning
Phase 2.1 Floorplanning | Checksum: 280003339

Time (s): cpu = 00:00:06 ; elapsed = 00:00:05 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 2.2 Physical Synthesis In Placer
INFO: [Physopt 32-65] No nets found for high-fanout optimization.
INFO: [Physopt 32-232] Optimized 0 net. Created 0 new instance.
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-670] No setup violation found.  DSP Register Optimization was not performed.
INFO: [Physopt 32-670] No setup violation found.  Shift Register Optimization was not performed.
INFO: [Physopt 32-670] No setup violation found.  BRAM Register Optimization was not performed.
INFO: [Physopt 32-949] No candidate nets found for HD net replication
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1530.242 ; gain = 0.000

Summary of Physical Synthesis Optimizations
============================================


----------------------------------------------------------------------------------------------------------------------------------------
|  Optimization                  |  Added Cells  |  Removed Cells  |  Optimized Cells/Nets  |  Dont Touch  |  Iterations  |  Elapsed   |
----------------------------------------------------------------------------------------------------------------------------------------
|  Very High Fanout              |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  DSP Register                  |            0  |              0  |                     0  |           0  |           0  |  00:00:00  |
|  Shift Register                |            0  |              0  |                     0  |           0  |           0  |  00:00:00  |
|  BRAM Register                 |            0  |              0  |                     0  |           0  |           0  |  00:00:00  |
|  HD Interface Net Replication  |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Total                         |            0  |              0  |                     0  |           0  |           2  |  00:00:00  |
----------------------------------------------------------------------------------------------------------------------------------------


Phase 2.2 Physical Synthesis In Placer | Checksum: 28672a871

Time (s): cpu = 00:00:24 ; elapsed = 00:00:21 . Memory (MB): peak = 1530.242 ; gain = 142.434
Phase 2 Global Placement | Checksum: 25c730738

Time (s): cpu = 00:00:25 ; elapsed = 00:00:21 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 3 Detail Placement

Phase 3.1 Commit Multi Column Macros
Phase 3.1 Commit Multi Column Macros | Checksum: 25c730738

Time (s): cpu = 00:00:25 ; elapsed = 00:00:21 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 3.2 Commit Most Macros & LUTRAMs
Phase 3.2 Commit Most Macros & LUTRAMs | Checksum: 2005f4b08

Time (s): cpu = 00:00:40 ; elapsed = 00:00:37 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 3.3 Area Swap Optimization
Phase 3.3 Area Swap Optimization | Checksum: 1dc37061b

Time (s): cpu = 00:00:40 ; elapsed = 00:00:37 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 3.4 Pipeline Register Optimization
Phase 3.4 Pipeline Register Optimization | Checksum: 1c5299668

Time (s): cpu = 00:00:40 ; elapsed = 00:00:37 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 3.5 Small Shape Detail Placement
Phase 3.5 Small Shape Detail Placement | Checksum: 231acd114

Time (s): cpu = 00:00:41 ; elapsed = 00:00:38 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 3.6 Re-assign LUT pins
Phase 3.6 Re-assign LUT pins | Checksum: 2a585f414

Time (s): cpu = 00:00:42 ; elapsed = 00:00:38 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 3.7 Pipeline Register Optimization
Phase 3.7 Pipeline Register Optimization | Checksum: 24e77647f

Time (s): cpu = 00:00:42 ; elapsed = 00:00:38 . Memory (MB): peak = 1530.242 ; gain = 142.434
Phase 3 Detail Placement | Checksum: 24e77647f

Time (s): cpu = 00:00:42 ; elapsed = 00:00:38 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 4 Post Placement Optimization and Clean-Up

Phase 4.1 Post Commit Optimization
INFO: [Timing 38-35] Done setting XDC timing constraints.

Phase 4.1.1 Post Placement Optimization
Post Placement Optimization Initialization | Checksum: 1b47deee2

Phase ******* BUFG Insertion
INFO: [Place 46-46] BUFG insertion identified 0 candidate nets, 0 success, 0 bufg driver replicated, 0 skipped for placement/routing, 0 skipped for timing, 0 skipped for netlist change reason
Phase ******* BUFG Insertion | Checksum: 1b47deee2

Time (s): cpu = 00:00:50 ; elapsed = 00:00:45 . Memory (MB): peak = 1530.242 ; gain = 142.434
INFO: [Place 30-746] Post Placement Timing Summary WNS=12.246. For the most accurate timing information please run report_timing.
Phase 4.1.1 Post Placement Optimization | Checksum: *********

Time (s): cpu = 00:00:50 ; elapsed = 00:00:45 . Memory (MB): peak = 1530.242 ; gain = 142.434
Phase 4.1 Post Commit Optimization | Checksum: *********

Time (s): cpu = 00:00:50 ; elapsed = 00:00:45 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 4.2 Post Placement Cleanup
Phase 4.2 Post Placement Cleanup | Checksum: *********

Time (s): cpu = 00:00:51 ; elapsed = 00:00:45 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 4.3 Placer Reporting
Phase 4.3 Placer Reporting | Checksum: *********

Time (s): cpu = 00:00:51 ; elapsed = 00:00:45 . Memory (MB): peak = 1530.242 ; gain = 142.434

Phase 4.4 Final Placement Cleanup
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1530.242 ; gain = 0.000
Phase 4.4 Final Placement Cleanup | Checksum: 1bc0df91e

Time (s): cpu = 00:00:51 ; elapsed = 00:00:45 . Memory (MB): peak = 1530.242 ; gain = 142.434
Phase 4 Post Placement Optimization and Clean-Up | Checksum: 1bc0df91e

Time (s): cpu = 00:00:51 ; elapsed = 00:00:45 . Memory (MB): peak = 1530.242 ; gain = 142.434
Ending Placer Task | Checksum: 1388c0115

Time (s): cpu = 00:00:51 ; elapsed = 00:00:45 . Memory (MB): peak = 1530.242 ; gain = 142.434
INFO: [Common 17-83] Releasing license: Implementation
58 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
place_design completed successfully
place_design: Time (s): cpu = 00:00:52 ; elapsed = 00:00:46 . Memory (MB): peak = 1530.242 ; gain = 142.434
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1530.242 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1530.242 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.591 . Memory (MB): peak = 1530.242 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC_placed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_io -file miniRV_SoC_io_placed.rpt
report_io: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.029 . Memory (MB): peak = 1530.242 ; gain = 0.000
INFO: [runtcl-4] Executing : report_utilization -file miniRV_SoC_utilization_placed.rpt -pb miniRV_SoC_utilization_placed.pb
INFO: [runtcl-4] Executing : report_control_sets -verbose -file miniRV_SoC_control_sets_placed.rpt
report_control_sets: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.038 . Memory (MB): peak = 1530.242 ; gain = 0.000
Command: route_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a35t'
Running DRC as a precondition to command route_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.


Starting Routing Task
INFO: [Route 35-254] Multithreading enabled for route_design using a maximum of 2 CPUs
Checksum: PlaceDB: ead59413 ConstDB: 0 ShapeSum: 4db66d02 RouteDB: 0

Phase 1 Build RT Design
Phase 1 Build RT Design | Checksum: 1050586e8

Time (s): cpu = 00:00:14 ; elapsed = 00:00:13 . Memory (MB): peak = 1600.941 ; gain = 70.699
Post Restoration Checksum: NetGraph: 2698e96f NumContArr: de6c9d79 Constraints: 0 Timing: 0

Phase 2 Router Initialization

Phase 2.1 Create Timer
Phase 2.1 Create Timer | Checksum: 1050586e8

Time (s): cpu = 00:00:14 ; elapsed = 00:00:13 . Memory (MB): peak = 1633.648 ; gain = 103.406

Phase 2.2 Fix Topology Constraints
Phase 2.2 Fix Topology Constraints | Checksum: 1050586e8

Time (s): cpu = 00:00:14 ; elapsed = 00:00:13 . Memory (MB): peak = 1640.344 ; gain = 110.102

Phase 2.3 Pre Route Cleanup
Phase 2.3 Pre Route Cleanup | Checksum: 1050586e8

Time (s): cpu = 00:00:14 ; elapsed = 00:00:13 . Memory (MB): peak = 1640.344 ; gain = 110.102
 Number of Nodes with overlaps = 0

Phase 2.4 Update Timing
Phase 2.4 Update Timing | Checksum: 24d9e11a6

Time (s): cpu = 00:00:21 ; elapsed = 00:00:19 . Memory (MB): peak = 1665.105 ; gain = 134.863
INFO: [Route 35-416] Intermediate Timing Summary | WNS=12.784 | TNS=0.000  | WHS=-0.068 | THS=-2.269 |

Phase 2 Router Initialization | Checksum: 1fd9068bd

Time (s): cpu = 00:00:23 ; elapsed = 00:00:20 . Memory (MB): peak = 1697.883 ; gain = 167.641

Phase 3 Initial Routing
Phase 3 Initial Routing | Checksum: 14ea4ecad

Time (s): cpu = 00:00:33 ; elapsed = 00:00:25 . Memory (MB): peak = 1816.047 ; gain = 285.805

Phase 4 Rip-up And Reroute

Phase 4.1 Global Iteration 0
 Number of Nodes with overlaps = 1483
 Number of Nodes with overlaps = 367
 Number of Nodes with overlaps = 54
 Number of Nodes with overlaps = 13
 Number of Nodes with overlaps = 4
 Number of Nodes with overlaps = 1
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=9.663  | TNS=0.000  | WHS=N/A    | THS=N/A    |

Phase 4.1 Global Iteration 0 | Checksum: 1272fb515

Time (s): cpu = 00:00:46 ; elapsed = 00:00:35 . Memory (MB): peak = 1816.047 ; gain = 285.805
Phase 4 Rip-up And Reroute | Checksum: 1272fb515

Time (s): cpu = 00:00:46 ; elapsed = 00:00:35 . Memory (MB): peak = 1816.047 ; gain = 285.805

Phase 5 Delay and Skew Optimization

Phase 5.1 Delay CleanUp

Phase 5.1.1 Update Timing
Phase 5.1.1 Update Timing | Checksum: cff0acb2

Time (s): cpu = 00:00:47 ; elapsed = 00:00:35 . Memory (MB): peak = 1816.047 ; gain = 285.805
INFO: [Route 35-416] Intermediate Timing Summary | WNS=9.742  | TNS=0.000  | WHS=N/A    | THS=N/A    |

Phase 5.1 Delay CleanUp | Checksum: cff0acb2

Time (s): cpu = 00:00:47 ; elapsed = 00:00:35 . Memory (MB): peak = 1816.047 ; gain = 285.805

Phase 5.2 Clock Skew Optimization
Phase 5.2 Clock Skew Optimization | Checksum: cff0acb2

Time (s): cpu = 00:00:47 ; elapsed = 00:00:35 . Memory (MB): peak = 1816.047 ; gain = 285.805
Phase 5 Delay and Skew Optimization | Checksum: cff0acb2

Time (s): cpu = 00:00:47 ; elapsed = 00:00:35 . Memory (MB): peak = 1816.047 ; gain = 285.805

Phase 6 Post Hold Fix

Phase 6.1 Hold Fix Iter

Phase 6.1.1 Update Timing
Phase 6.1.1 Update Timing | Checksum: 13127c261

Time (s): cpu = 00:00:48 ; elapsed = 00:00:36 . Memory (MB): peak = 1816.047 ; gain = 285.805
INFO: [Route 35-416] Intermediate Timing Summary | WNS=9.742  | TNS=0.000  | WHS=0.210  | THS=0.000  |

Phase 6.1 Hold Fix Iter | Checksum: cd809ad8

Time (s): cpu = 00:00:48 ; elapsed = 00:00:36 . Memory (MB): peak = 1816.047 ; gain = 285.805
Phase 6 Post Hold Fix | Checksum: cd809ad8

Time (s): cpu = 00:00:48 ; elapsed = 00:00:36 . Memory (MB): peak = 1816.047 ; gain = 285.805

Phase 7 Route finalize

Router Utilization Summary
  Global Vertical Routing Utilization    = 15.8884 %
  Global Horizontal Routing Utilization  = 10.9711 %
  Routable Net Status*
  *Does not include unroutable nets such as driverless and loadless.
  Run report_route_status for detailed report.
  Number of Failed Nets               = 0
  Number of Unrouted Nets             = 0
  Number of Partially Routed Nets     = 0
  Number of Node Overlaps             = 0

Phase 7 Route finalize | Checksum: 12dcb9789

Time (s): cpu = 00:00:48 ; elapsed = 00:00:36 . Memory (MB): peak = 1816.047 ; gain = 285.805

Phase 8 Verifying routed nets

 Verification completed successfully
Phase 8 Verifying routed nets | Checksum: 12dcb9789

Time (s): cpu = 00:00:48 ; elapsed = 00:00:36 . Memory (MB): peak = 1816.047 ; gain = 285.805

Phase 9 Depositing Routes
Phase 9 Depositing Routes | Checksum: 131ed8e2a

Time (s): cpu = 00:00:49 ; elapsed = 00:00:37 . Memory (MB): peak = 1816.047 ; gain = 285.805

Phase 10 Post Router Timing
INFO: [Route 35-57] Estimated Timing Summary | WNS=9.742  | TNS=0.000  | WHS=0.210  | THS=0.000  |

INFO: [Route 35-327] The final timing numbers are based on the router estimated timing analysis. For a complete and accurate timing signoff, please run report_timing_summary.
Phase 10 Post Router Timing | Checksum: 131ed8e2a

Time (s): cpu = 00:00:49 ; elapsed = 00:00:37 . Memory (MB): peak = 1816.047 ; gain = 285.805
INFO: [Route 35-16] Router Completed Successfully

Time (s): cpu = 00:00:49 ; elapsed = 00:00:37 . Memory (MB): peak = 1816.047 ; gain = 285.805

Routing Is Done.
INFO: [Common 17-83] Releasing license: Implementation
76 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
route_design completed successfully
route_design: Time (s): cpu = 00:00:50 ; elapsed = 00:00:37 . Memory (MB): peak = 1816.047 ; gain = 285.805
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1816.047 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1816.047 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.717 . Memory (MB): peak = 1816.047 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC_routed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file miniRV_SoC_drc_routed.rpt -pb miniRV_SoC_drc_routed.pb -rpx miniRV_SoC_drc_routed.rpx
Command: report_drc -file miniRV_SoC_drc_routed.rpt -pb miniRV_SoC_drc_routed.pb -rpx miniRV_SoC_drc_routed.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC_drc_routed.rpt.
report_drc completed successfully
INFO: [runtcl-4] Executing : report_methodology -file miniRV_SoC_methodology_drc_routed.rpt -pb miniRV_SoC_methodology_drc_routed.pb -rpx miniRV_SoC_methodology_drc_routed.rpx
Command: report_methodology -file miniRV_SoC_methodology_drc_routed.rpt -pb miniRV_SoC_methodology_drc_routed.pb -rpx miniRV_SoC_methodology_drc_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [DRC 23-133] Running Methodology with 2 threads
INFO: [Coretcl 2-1520] The results of Report Methodology are in file C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC_methodology_drc_routed.rpt.
report_methodology completed successfully
report_methodology: Time (s): cpu = 00:00:09 ; elapsed = 00:00:05 . Memory (MB): peak = 1905.895 ; gain = 89.848
INFO: [runtcl-4] Executing : report_power -file miniRV_SoC_power_routed.rpt -pb miniRV_SoC_power_summary_routed.pb -rpx miniRV_SoC_power_routed.rpx
Command: report_power -file miniRV_SoC_power_routed.rpt -pb miniRV_SoC_power_summary_routed.pb -rpx miniRV_SoC_power_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
Running Vector-less Activity Propagation...

Finished Running Vector-less Activity Propagation
88 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
report_power completed successfully
INFO: [runtcl-4] Executing : report_route_status -file miniRV_SoC_route_status.rpt -pb miniRV_SoC_route_status.pb
INFO: [runtcl-4] Executing : report_timing_summary -max_paths 10 -file miniRV_SoC_timing_summary_routed.rpt -pb miniRV_SoC_timing_summary_routed.pb -rpx miniRV_SoC_timing_summary_routed.rpx -warn_on_violation 
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
INFO: [runtcl-4] Executing : report_incremental_reuse -file miniRV_SoC_incremental_reuse_routed.rpt
INFO: [Vivado_Tcl 4-1062] Incremental flow is disabled. No incremental reuse Info to report.
INFO: [runtcl-4] Executing : report_clock_utilization -file miniRV_SoC_clock_utilization_routed.rpt
INFO: [runtcl-4] Executing : report_bus_skew -warn_on_violation -file miniRV_SoC_bus_skew_routed.rpt -pb miniRV_SoC_bus_skew_routed.pb -rpx miniRV_SoC_bus_skew_routed.rpx
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
INFO: [Common 17-206] Exiting Vivado at Thu Jul  3 17:13:46 2025...
#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Thu Jul  3 17:14:45 2025
# Process ID: 29076
# Current directory: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1
# Command line: vivado.exe -log miniRV_SoC.vdi -applog -product Vivado -messageDb vivado.pb -mode batch -source miniRV_SoC.tcl -notrace
# Log file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC.vdi
# Journal file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1\vivado.jou
#-----------------------------------------------------------
source miniRV_SoC.tcl -notrace
Command: open_checkpoint miniRV_SoC_routed.dcp

Starting open_checkpoint Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.016 . Memory (MB): peak = 250.980 ; gain = 0.000
INFO: [Netlist 29-17] Analyzing 2555 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 1 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2018.3
INFO: [Device 21-403] Loading part xc7a35tcsg324-1
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Timing 38-478] Restoring timing data from binary archive.
INFO: [Timing 38-479] Binary timing data restore complete.
INFO: [Project 1-856] Restoring constraints from binary archive.
INFO: [Project 1-853] Binary constraint restore complete.
Reading XDEF placement.
Reading placer database...
Reading XDEF routing.
Read XDEF File: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.707 . Memory (MB): peak = 1277.234 ; gain = 16.488
Restored from archive | CPU: 1.000000 secs | Memory: 0.000000 MB |
Finished XDEF File Restore: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.707 . Memory (MB): peak = 1277.234 ; gain = 16.488
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1277.273 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
  A total of 2060 instances were transformed.
  RAM256X1S => RAM256X1S (MUXF7, MUXF7, MUXF8, RAMS64E, RAMS64E, RAMS64E, RAMS64E): 2048 instances
  RAM32M => RAM32M (RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMS32, RAMS32): 12 instances

INFO: [Project 1-604] Checkpoint was created with Vivado v2018.3 (64-bit) build 2405991
open_checkpoint: Time (s): cpu = 00:00:10 ; elapsed = 00:00:12 . Memory (MB): peak = 1277.273 ; gain = 1026.293
Command: write_bitstream -force miniRV_SoC.bit
Attempting to get a license for feature 'Implementation' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a35t'
Running DRC as a precondition to command write_bitstream
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'E:/Vivado/2018.3/data/ip'.
INFO: [DRC 23-27] Running DRC with 2 threads
WARNING: [DRC CFGBVS-1] Missing CFGBVS and CONFIG_VOLTAGE Design Properties: Neither the CFGBVS nor CONFIG_VOLTAGE voltage property is set in the current_design.  Configuration bank voltage select (CFGBVS) must be set to VCCO or GND, and CONFIG_VOLTAGE must be set to the correct configuration voltage, in order to determine the I/O voltage support for the pins in bank 0.  It is suggested to specify these either using the 'Edit Device Properties' function in the GUI or directly in the XDC file using the following syntax:

 set_property CFGBVS value1 [current_design]
 #where value1 is either VCCO or GND

 set_property CONFIG_VOLTAGE value2 [current_design]
 #where value2 is the voltage provided to configuration bank 0

Refer to the device configuration user guide for more information.
INFO: [Vivado 12-3199] DRC finished with 0 Errors, 1 Warnings
INFO: [Vivado 12-3200] Please refer to the DRC report (report_drc) for more information.
INFO: [Designutils 20-2272] Running write_bitstream with 2 threads.
Loading data files...
Loading site data...
Loading route data...
Processing options...
Creating bitmap...
Creating bitstream...
Writing bitstream ./miniRV_SoC.bit...
INFO: [Vivado 12-1842] Bitgen Completed Successfully.
INFO: [Project 1-120] WebTalk data collection is mandatory when using a WebPACK part without a full Vivado license. To see the specific WebTalk data collected for your design, open the usage_statistics_webtalk.html or usage_statistics_webtalk.xml file in the implementation directory.
INFO: [Common 17-83] Releasing license: Implementation
22 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
write_bitstream completed successfully
write_bitstream: Time (s): cpu = 00:00:15 ; elapsed = 00:00:12 . Memory (MB): peak = 1792.586 ; gain = 515.312
INFO: [Common 17-206] Exiting Vivado at Thu Jul  3 17:15:10 2025...
