Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Thu Jul  3 17:13:46 2025
| Host         : L running 64-bit major release  (build 9200)
| Command      : report_timing_summary -max_paths 10 -file miniRV_SoC_timing_summary_routed.rpt -pb miniRV_SoC_timing_summary_routed.pb -rpx miniRV_SoC_timing_summary_routed.rpx -warn_on_violation
| Design       : miniRV_SoC
| Device       : 7a35t-csg324
| Speed File   : -1  PRODUCTION 1.23 2018-06-13
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Timing Summary Report

------------------------------------------------------------------------------------------------
| Timer Settings
| --------------
------------------------------------------------------------------------------------------------

  Enable Multi Corner Analysis               :  Yes
  Enable Pessimism Removal                   :  Yes
  Pessimism Removal Resolution               :  Nearest Common Node
  Enable Input Delay Default Clock           :  No
  Enable Preset / Clear Arcs                 :  No
  Disable Flight Delays                      :  No
  Ignore I/O Paths                           :  No
  Timing Early Launch at Borrowing Latches   :  false

  Corner  Analyze    Analyze    
  Name    Max Paths  Min Paths  
  ------  ---------  ---------  
  Slow    Yes        Yes        
  Fast    Yes        Yes        



check_timing report

Table of Contents
-----------------
1. checking no_clock
2. checking constant_clock
3. checking pulse_width_clock
4. checking unconstrained_internal_endpoints
5. checking no_input_delay
6. checking no_output_delay
7. checking multiple_clock
8. checking generated_clocks
9. checking loops
10. checking partial_input_delay
11. checking partial_output_delay
12. checking latch_loops

1. checking no_clock
--------------------
 There are 0 register/latch pins with no clock.


2. checking constant_clock
--------------------------
 There are 0 register/latch pins with constant_clock.


3. checking pulse_width_clock
-----------------------------
 There are 0 register/latch pins which need pulse_width check


4. checking unconstrained_internal_endpoints
--------------------------------------------
 There are 0 pins that are not constrained for maximum delay.

 There are 0 pins that are not constrained for maximum delay due to constant clock.


5. checking no_input_delay
--------------------------
 There are 22 input ports with no input delay specified. (HIGH)

 There are 0 input ports with no input delay but user has a false path constraint.


6. checking no_output_delay
---------------------------
 There are 38 ports with no output delay specified. (HIGH)

 There are 0 ports with no output delay but user has a false path constraint

 There are 0 ports with no output delay but with a timing clock defined on it or propagating through it


7. checking multiple_clock
--------------------------
 There are 0 register/latch pins with multiple clocks.


8. checking generated_clocks
----------------------------
 There are 0 generated clocks that are not connected to a clock source.


9. checking loops
-----------------
 There are 0 combinational loops in the design.


10. checking partial_input_delay
--------------------------------
 There are 0 input ports with partial input delay specified.


11. checking partial_output_delay
---------------------------------
 There are 0 ports with partial output delay specified.


12. checking latch_loops
------------------------
 There are 0 combinational latch loops in the design through latch input



------------------------------------------------------------------------------------------------
| Design Timing Summary
| ---------------------
------------------------------------------------------------------------------------------------

    WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
    -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
      9.750        0.000                      0                82977        0.218        0.000                      0                82977        3.000        0.000                       0                  8605  


All user specified timing constraints are met.


------------------------------------------------------------------------------------------------
| Clock Summary
| -------------
------------------------------------------------------------------------------------------------

Clock              Waveform(ns)       Period(ns)      Frequency(MHz)
-----              ------------       ----------      --------------
fpga_clk           {0.000 5.000}      10.000          100.000         
  clk_out1_cpuclk  {0.000 20.000}     40.000          25.000          
  clkfbout_cpuclk  {0.000 20.000}     40.000          25.000          


------------------------------------------------------------------------------------------------
| Intra Clock Table
| -----------------
------------------------------------------------------------------------------------------------

Clock                  WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
-----                  -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
fpga_clk                                                                                                                                                             3.000        0.000                       0                     1  
  clk_out1_cpuclk        9.750        0.000                      0                82977        0.218        0.000                      0                82977       18.750        0.000                       0                  8601  
  clkfbout_cpuclk                                                                                                                                                   12.633        0.000                       0                     3  


------------------------------------------------------------------------------------------------
| Inter Clock Table
| -----------------
------------------------------------------------------------------------------------------------

From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  


------------------------------------------------------------------------------------------------
| Other Path Groups Table
| -----------------------
------------------------------------------------------------------------------------------------

Path Group    From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    ----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  


------------------------------------------------------------------------------------------------
| Timing Details
| --------------
------------------------------------------------------------------------------------------------


---------------------------------------------------------------------------------------------------
From Clock:  fpga_clk
  To Clock:  fpga_clk

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack        3.000ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         fpga_clk
Waveform(ns):       { 0.000 5.000 }
Period(ns):         10.000
Sources:            { fpga_clk }

Check Type        Corner  Lib Pin           Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     PLLE2_ADV/CLKIN1  n/a            1.249         10.000      8.751      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Max Period        n/a     PLLE2_ADV/CLKIN1  n/a            52.633        10.000      42.633     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Low Pulse Width   Slow    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Low Pulse Width   Fast    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
High Pulse Width  Slow    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
High Pulse Width  Fast    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1



---------------------------------------------------------------------------------------------------
From Clock:  clk_out1_cpuclk
  To Clock:  clk_out1_cpuclk

Setup :            0  Failing Endpoints,  Worst Slack        9.750ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.218ns,  Total Violation        0.000ns
PW    :            0  Failing Endpoints,  Worst Slack       18.750ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             9.750ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r1_0_31_18_23/RAMA/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        29.870ns  (logic 4.090ns (13.691%)  route 25.780ns (86.309%))
  Logic Levels:           20  (LUT3=1 LUT5=4 LUT6=9 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.359ns = ( 40.359 - 40.000 ) 
    Source Clock Delay      (SCD):    0.235ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.569     0.235    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X48Y8          FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y8          FDCE (Prop_fdce_C_Q)         0.456     0.691 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=56, routed)          1.613     2.304    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X39Y6          LUT6 (Prop_lut6_I0_O)        0.124     2.428 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     2.861    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X39Y6          LUT6 (Prop_lut6_I1_O)        0.124     2.985 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.582     3.568    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X40Y8          LUT5 (Prop_lut5_I2_O)        0.124     3.692 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.436     5.128    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRC0
    SLICE_X42Y13         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.153     5.281 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMC/O
                         net (fo=2, routed)           0.803     6.084    Core_cpu/U_RF/rD20[4]
    SLICE_X41Y13         LUT6 (Prop_lut6_I0_O)        0.331     6.415 r  Core_cpu/U_RF/Mem_DRAM_i_42/O
                         net (fo=260, routed)         0.754     7.169    Core_cpu/U_RF/bbstub_spo[20][4]
    SLICE_X44Y9          LUT6 (Prop_lut6_I5_O)        0.124     7.293 r  Core_cpu/U_RF/C0_carry__0_i_10/O
                         net (fo=111, routed)         1.389     8.682    Core_cpu/U_RF/alu_B[4]
    SLICE_X58Y3          LUT5 (Prop_lut5_I3_O)        0.124     8.806 f  Core_cpu/U_RF/pc[1]_i_11/O
                         net (fo=2, routed)           0.509     9.315    Core_cpu/U_RF/pc[1]_i_11_n_0
    SLICE_X57Y3          LUT5 (Prop_lut5_I2_O)        0.124     9.439 f  Core_cpu/U_RF/pc[1]_i_7/O
                         net (fo=6, routed)           0.854    10.293    Core_cpu/U_RF/pc[1]_i_7_n_0
    SLICE_X60Y3          LUT5 (Prop_lut5_I4_O)        0.124    10.417 f  Core_cpu/U_RF/Mem_DRAM_i_162/O
                         net (fo=2, routed)           0.434    10.851    Core_cpu/U_RF/Mem_DRAM_i_162_n_0
    SLICE_X60Y4          LUT6 (Prop_lut6_I2_O)        0.124    10.975 r  Core_cpu/U_RF/Mem_DRAM_i_95/O
                         net (fo=1, routed)           1.236    12.211    Core_cpu/U_RF/Mem_DRAM_i_95_n_0
    SLICE_X53Y17         LUT6 (Prop_lut6_I4_O)        0.124    12.335 r  Core_cpu/U_RF/Mem_DRAM_i_13/O
                         net (fo=8202, routed)        6.469    18.803    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18/A1
    SLICE_X12Y140        RAMS64E (Prop_rams64e_ADR1_O)
                                                      0.307    19.111 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18/RAMS64E_A/O
                         net (fo=1, routed)           0.000    19.111    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18/OA
    SLICE_X12Y140        MUXF7 (Prop_muxf7_I1_O)      0.214    19.325 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18/F7.A/O
                         net (fo=1, routed)           0.000    19.325    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18/O1
    SLICE_X12Y140        MUXF8 (Prop_muxf8_I1_O)      0.088    19.413 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18/F8/O
                         net (fo=1, routed)           1.605    21.018    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18_n_0
    SLICE_X13Y123        LUT6 (Prop_lut6_I0_O)        0.319    21.337 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0_i_16/O
                         net (fo=1, routed)           0.000    21.337    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0_i_16_n_0
    SLICE_X13Y123        MUXF7 (Prop_muxf7_I1_O)      0.217    21.554 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0_i_6/O
                         net (fo=1, routed)           0.000    21.554    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0_i_6_n_0
    SLICE_X13Y123        MUXF8 (Prop_muxf8_I1_O)      0.094    21.648 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0_i_1/O
                         net (fo=1, routed)           1.019    22.666    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0_i_1_n_0
    SLICE_X15Y119        LUT6 (Prop_lut6_I0_O)        0.316    22.982 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0/O
                         net (fo=1, routed)           5.038    28.020    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1_2[18]
    SLICE_X48Y19         LUT3 (Prop_lut3_I1_O)        0.152    28.172 r  Core_cpu/U_RF/registers_reg_r1_0_31_18_23_i_11/O
                         net (fo=1, routed)           0.624    28.796    Core_cpu/U_RF/registers_reg_r1_0_31_18_23_i_11_n_0
    SLICE_X49Y19         LUT6 (Prop_lut6_I4_O)        0.326    29.122 r  Core_cpu/U_RF/registers_reg_r1_0_31_18_23_i_2/O
                         net (fo=2, routed)           0.983    30.105    Core_cpu/U_RF/registers_reg_r1_0_31_18_23/DIA0
    SLICE_X52Y10         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_18_23/RAMA/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.450    40.359    Core_cpu/U_RF/registers_reg_r1_0_31_18_23/WCLK
    SLICE_X52Y10         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_18_23/RAMA/CLK
                         clock pessimism             -0.163    40.196    
                         clock uncertainty           -0.180    40.016    
    SLICE_X52Y10         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.161    39.855    Core_cpu/U_RF/registers_reg_r1_0_31_18_23/RAMA
  -------------------------------------------------------------------
                         required time                         39.855    
                         arrival time                         -30.105    
  -------------------------------------------------------------------
                         slack                                  9.750    

Slack (MET) :             9.889ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r2_0_31_18_23/RAMA/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        29.730ns  (logic 4.090ns (13.755%)  route 25.640ns (86.245%))
  Logic Levels:           20  (LUT3=1 LUT5=4 LUT6=9 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.040ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.358ns = ( 40.358 - 40.000 ) 
    Source Clock Delay      (SCD):    0.235ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.569     0.235    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X48Y8          FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y8          FDCE (Prop_fdce_C_Q)         0.456     0.691 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=56, routed)          1.613     2.304    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X39Y6          LUT6 (Prop_lut6_I0_O)        0.124     2.428 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     2.861    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X39Y6          LUT6 (Prop_lut6_I1_O)        0.124     2.985 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.582     3.568    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X40Y8          LUT5 (Prop_lut5_I2_O)        0.124     3.692 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.436     5.128    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRC0
    SLICE_X42Y13         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.153     5.281 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMC/O
                         net (fo=2, routed)           0.803     6.084    Core_cpu/U_RF/rD20[4]
    SLICE_X41Y13         LUT6 (Prop_lut6_I0_O)        0.331     6.415 r  Core_cpu/U_RF/Mem_DRAM_i_42/O
                         net (fo=260, routed)         0.754     7.169    Core_cpu/U_RF/bbstub_spo[20][4]
    SLICE_X44Y9          LUT6 (Prop_lut6_I5_O)        0.124     7.293 r  Core_cpu/U_RF/C0_carry__0_i_10/O
                         net (fo=111, routed)         1.389     8.682    Core_cpu/U_RF/alu_B[4]
    SLICE_X58Y3          LUT5 (Prop_lut5_I3_O)        0.124     8.806 f  Core_cpu/U_RF/pc[1]_i_11/O
                         net (fo=2, routed)           0.509     9.315    Core_cpu/U_RF/pc[1]_i_11_n_0
    SLICE_X57Y3          LUT5 (Prop_lut5_I2_O)        0.124     9.439 f  Core_cpu/U_RF/pc[1]_i_7/O
                         net (fo=6, routed)           0.854    10.293    Core_cpu/U_RF/pc[1]_i_7_n_0
    SLICE_X60Y3          LUT5 (Prop_lut5_I4_O)        0.124    10.417 f  Core_cpu/U_RF/Mem_DRAM_i_162/O
                         net (fo=2, routed)           0.434    10.851    Core_cpu/U_RF/Mem_DRAM_i_162_n_0
    SLICE_X60Y4          LUT6 (Prop_lut6_I2_O)        0.124    10.975 r  Core_cpu/U_RF/Mem_DRAM_i_95/O
                         net (fo=1, routed)           1.236    12.211    Core_cpu/U_RF/Mem_DRAM_i_95_n_0
    SLICE_X53Y17         LUT6 (Prop_lut6_I4_O)        0.124    12.335 r  Core_cpu/U_RF/Mem_DRAM_i_13/O
                         net (fo=8202, routed)        6.469    18.803    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18/A1
    SLICE_X12Y140        RAMS64E (Prop_rams64e_ADR1_O)
                                                      0.307    19.111 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18/RAMS64E_A/O
                         net (fo=1, routed)           0.000    19.111    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18/OA
    SLICE_X12Y140        MUXF7 (Prop_muxf7_I1_O)      0.214    19.325 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18/F7.A/O
                         net (fo=1, routed)           0.000    19.325    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18/O1
    SLICE_X12Y140        MUXF8 (Prop_muxf8_I1_O)      0.088    19.413 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18/F8/O
                         net (fo=1, routed)           1.605    21.018    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_18_18_n_0
    SLICE_X13Y123        LUT6 (Prop_lut6_I0_O)        0.319    21.337 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0_i_16/O
                         net (fo=1, routed)           0.000    21.337    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0_i_16_n_0
    SLICE_X13Y123        MUXF7 (Prop_muxf7_I1_O)      0.217    21.554 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0_i_6/O
                         net (fo=1, routed)           0.000    21.554    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0_i_6_n_0
    SLICE_X13Y123        MUXF8 (Prop_muxf8_I1_O)      0.094    21.648 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0_i_1/O
                         net (fo=1, routed)           1.019    22.666    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0_i_1_n_0
    SLICE_X15Y119        LUT6 (Prop_lut6_I0_O)        0.316    22.982 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[18]_INST_0/O
                         net (fo=1, routed)           5.038    28.020    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1_2[18]
    SLICE_X48Y19         LUT3 (Prop_lut3_I1_O)        0.152    28.172 r  Core_cpu/U_RF/registers_reg_r1_0_31_18_23_i_11/O
                         net (fo=1, routed)           0.624    28.796    Core_cpu/U_RF/registers_reg_r1_0_31_18_23_i_11_n_0
    SLICE_X49Y19         LUT6 (Prop_lut6_I4_O)        0.326    29.122 r  Core_cpu/U_RF/registers_reg_r1_0_31_18_23_i_2/O
                         net (fo=2, routed)           0.843    29.965    Core_cpu/U_RF/registers_reg_r2_0_31_18_23/DIA0
    SLICE_X52Y11         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_18_23/RAMA/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.449    40.358    Core_cpu/U_RF/registers_reg_r2_0_31_18_23/WCLK
    SLICE_X52Y11         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_18_23/RAMA/CLK
                         clock pessimism             -0.163    40.195    
                         clock uncertainty           -0.180    40.015    
    SLICE_X52Y11         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.161    39.854    Core_cpu/U_RF/registers_reg_r2_0_31_18_23/RAMA
  -------------------------------------------------------------------
                         required time                         39.854    
                         arrival time                         -29.965    
  -------------------------------------------------------------------
                         slack                                  9.889    

Slack (MET) :             10.194ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r2_0_31_24_29/RAMC_D1/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        29.336ns  (logic 3.584ns (12.217%)  route 25.752ns (87.783%))
  Logic Levels:           19  (LUT2=1 LUT3=1 LUT5=1 LUT6=10 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.042ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.356ns = ( 40.356 - 40.000 ) 
    Source Clock Delay      (SCD):    0.235ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.569     0.235    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X48Y8          FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y8          FDCE (Prop_fdce_C_Q)         0.456     0.691 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=56, routed)          1.613     2.304    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X39Y6          LUT6 (Prop_lut6_I0_O)        0.124     2.428 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     2.861    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X39Y6          LUT6 (Prop_lut6_I1_O)        0.124     2.985 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.582     3.568    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X40Y8          LUT5 (Prop_lut5_I2_O)        0.124     3.692 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.436     5.128    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRC0
    SLICE_X42Y13         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.153     5.281 f  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMC/O
                         net (fo=2, routed)           0.803     6.084    Core_cpu/U_RF/rD20[4]
    SLICE_X41Y13         LUT6 (Prop_lut6_I0_O)        0.331     6.415 f  Core_cpu/U_RF/Mem_DRAM_i_42/O
                         net (fo=260, routed)         0.754     7.169    Core_cpu/U_RF/bbstub_spo[20][4]
    SLICE_X44Y9          LUT6 (Prop_lut6_I5_O)        0.124     7.293 f  Core_cpu/U_RF/C0_carry__0_i_10/O
                         net (fo=111, routed)         1.420     8.713    Core_cpu/U_RF/alu_B[4]
    SLICE_X57Y4          LUT2 (Prop_lut2_I1_O)        0.124     8.837 f  Core_cpu/U_RF/pc[29]_i_9/O
                         net (fo=23, routed)          0.654     9.491    Core_cpu/U_RF/pc[29]_i_9_n_0
    SLICE_X53Y3          LUT6 (Prop_lut6_I2_O)        0.124     9.615 f  Core_cpu/U_RF/Mem_DRAM_i_140/O
                         net (fo=2, routed)           0.716    10.331    Core_cpu/U_RF/Mem_DRAM_i_140_n_0
    SLICE_X58Y4          LUT6 (Prop_lut6_I4_O)        0.124    10.455 f  Core_cpu/U_RF/Mem_DRAM_i_81/O
                         net (fo=1, routed)           1.195    11.650    Core_cpu/U_RF/Mem_DRAM_i_81_n_0
    SLICE_X55Y6          LUT6 (Prop_lut6_I2_O)        0.124    11.774 r  Core_cpu/U_RF/Mem_DRAM_i_9/O
                         net (fo=8197, routed)        7.131    18.905    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29/A5
    SLICE_X2Y144         RAMS64E (Prop_rams64e_ADR5_O)
                                                      0.124    19.029 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29/RAMS64E_D/O
                         net (fo=1, routed)           0.000    19.029    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29/OD
    SLICE_X2Y144         MUXF7 (Prop_muxf7_I0_O)      0.241    19.270 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29/F7.B/O
                         net (fo=1, routed)           0.000    19.270    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29/O0
    SLICE_X2Y144         MUXF8 (Prop_muxf8_I0_O)      0.098    19.368 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29/F8/O
                         net (fo=1, routed)           1.260    20.627    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29_n_0
    SLICE_X3Y129         LUT6 (Prop_lut6_I1_O)        0.319    20.946 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0_i_19/O
                         net (fo=1, routed)           0.000    20.946    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0_i_19_n_0
    SLICE_X3Y129         MUXF7 (Prop_muxf7_I0_O)      0.212    21.158 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0_i_8/O
                         net (fo=1, routed)           0.000    21.158    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0_i_8_n_0
    SLICE_X3Y129         MUXF8 (Prop_muxf8_I1_O)      0.094    21.252 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0_i_2/O
                         net (fo=1, routed)           1.120    22.373    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0_i_2_n_0
    SLICE_X7Y125         LUT6 (Prop_lut6_I1_O)        0.316    22.689 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0/O
                         net (fo=1, routed)           4.757    27.446    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1_2[29]
    SLICE_X44Y29         LUT3 (Prop_lut3_I1_O)        0.124    27.570 r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_21/O
                         net (fo=1, routed)           0.962    28.532    Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_21_n_0
    SLICE_X45Y20         LUT6 (Prop_lut6_I5_O)        0.124    28.656 r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_5/O
                         net (fo=2, routed)           0.915    29.571    Core_cpu/U_RF/registers_reg_r2_0_31_24_29/DIC1
    SLICE_X52Y14         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_24_29/RAMC_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.447    40.356    Core_cpu/U_RF/registers_reg_r2_0_31_24_29/WCLK
    SLICE_X52Y14         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_24_29/RAMC_D1/CLK
                         clock pessimism             -0.163    40.193    
                         clock uncertainty           -0.180    40.013    
    SLICE_X52Y14         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.249    39.764    Core_cpu/U_RF/registers_reg_r2_0_31_24_29/RAMC_D1
  -------------------------------------------------------------------
                         required time                         39.764    
                         arrival time                         -29.571    
  -------------------------------------------------------------------
                         slack                                 10.194    

Slack (MET) :             10.295ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r1_0_31_24_29/RAMC_D1/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        29.235ns  (logic 3.584ns (12.259%)  route 25.651ns (87.741%))
  Logic Levels:           19  (LUT2=1 LUT3=1 LUT5=1 LUT6=10 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.042ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.356ns = ( 40.356 - 40.000 ) 
    Source Clock Delay      (SCD):    0.235ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.569     0.235    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X48Y8          FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y8          FDCE (Prop_fdce_C_Q)         0.456     0.691 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=56, routed)          1.613     2.304    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X39Y6          LUT6 (Prop_lut6_I0_O)        0.124     2.428 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     2.861    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X39Y6          LUT6 (Prop_lut6_I1_O)        0.124     2.985 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.582     3.568    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X40Y8          LUT5 (Prop_lut5_I2_O)        0.124     3.692 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.436     5.128    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRC0
    SLICE_X42Y13         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.153     5.281 f  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMC/O
                         net (fo=2, routed)           0.803     6.084    Core_cpu/U_RF/rD20[4]
    SLICE_X41Y13         LUT6 (Prop_lut6_I0_O)        0.331     6.415 f  Core_cpu/U_RF/Mem_DRAM_i_42/O
                         net (fo=260, routed)         0.754     7.169    Core_cpu/U_RF/bbstub_spo[20][4]
    SLICE_X44Y9          LUT6 (Prop_lut6_I5_O)        0.124     7.293 f  Core_cpu/U_RF/C0_carry__0_i_10/O
                         net (fo=111, routed)         1.420     8.713    Core_cpu/U_RF/alu_B[4]
    SLICE_X57Y4          LUT2 (Prop_lut2_I1_O)        0.124     8.837 f  Core_cpu/U_RF/pc[29]_i_9/O
                         net (fo=23, routed)          0.654     9.491    Core_cpu/U_RF/pc[29]_i_9_n_0
    SLICE_X53Y3          LUT6 (Prop_lut6_I2_O)        0.124     9.615 f  Core_cpu/U_RF/Mem_DRAM_i_140/O
                         net (fo=2, routed)           0.716    10.331    Core_cpu/U_RF/Mem_DRAM_i_140_n_0
    SLICE_X58Y4          LUT6 (Prop_lut6_I4_O)        0.124    10.455 f  Core_cpu/U_RF/Mem_DRAM_i_81/O
                         net (fo=1, routed)           1.195    11.650    Core_cpu/U_RF/Mem_DRAM_i_81_n_0
    SLICE_X55Y6          LUT6 (Prop_lut6_I2_O)        0.124    11.774 r  Core_cpu/U_RF/Mem_DRAM_i_9/O
                         net (fo=8197, routed)        7.131    18.905    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29/A5
    SLICE_X2Y144         RAMS64E (Prop_rams64e_ADR5_O)
                                                      0.124    19.029 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29/RAMS64E_D/O
                         net (fo=1, routed)           0.000    19.029    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29/OD
    SLICE_X2Y144         MUXF7 (Prop_muxf7_I0_O)      0.241    19.270 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29/F7.B/O
                         net (fo=1, routed)           0.000    19.270    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29/O0
    SLICE_X2Y144         MUXF8 (Prop_muxf8_I0_O)      0.098    19.368 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29/F8/O
                         net (fo=1, routed)           1.260    20.627    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_29_29_n_0
    SLICE_X3Y129         LUT6 (Prop_lut6_I1_O)        0.319    20.946 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0_i_19/O
                         net (fo=1, routed)           0.000    20.946    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0_i_19_n_0
    SLICE_X3Y129         MUXF7 (Prop_muxf7_I0_O)      0.212    21.158 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0_i_8/O
                         net (fo=1, routed)           0.000    21.158    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0_i_8_n_0
    SLICE_X3Y129         MUXF8 (Prop_muxf8_I1_O)      0.094    21.252 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0_i_2/O
                         net (fo=1, routed)           1.120    22.373    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0_i_2_n_0
    SLICE_X7Y125         LUT6 (Prop_lut6_I1_O)        0.316    22.689 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[29]_INST_0/O
                         net (fo=1, routed)           4.757    27.446    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1_2[29]
    SLICE_X44Y29         LUT3 (Prop_lut3_I1_O)        0.124    27.570 r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_21/O
                         net (fo=1, routed)           0.962    28.532    Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_21_n_0
    SLICE_X45Y20         LUT6 (Prop_lut6_I5_O)        0.124    28.656 r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_5/O
                         net (fo=2, routed)           0.814    29.470    Core_cpu/U_RF/registers_reg_r1_0_31_24_29/DIC1
    SLICE_X52Y13         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29/RAMC_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.447    40.356    Core_cpu/U_RF/registers_reg_r1_0_31_24_29/WCLK
    SLICE_X52Y13         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29/RAMC_D1/CLK
                         clock pessimism             -0.163    40.193    
                         clock uncertainty           -0.180    40.013    
    SLICE_X52Y13         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.249    39.764    Core_cpu/U_RF/registers_reg_r1_0_31_24_29/RAMC_D1
  -------------------------------------------------------------------
                         required time                         39.764    
                         arrival time                         -29.470    
  -------------------------------------------------------------------
                         slack                                 10.295    

Slack (MET) :             11.071ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r2_0_31_6_11/RAMB_D1/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        28.476ns  (logic 5.266ns (18.493%)  route 23.210ns (81.507%))
  Logic Levels:           20  (LUT3=1 LUT5=4 LUT6=9 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.046ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.352ns = ( 40.352 - 40.000 ) 
    Source Clock Delay      (SCD):    0.235ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.569     0.235    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X48Y8          FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y8          FDCE (Prop_fdce_C_Q)         0.456     0.691 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=56, routed)          1.613     2.304    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X39Y6          LUT6 (Prop_lut6_I0_O)        0.124     2.428 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     2.861    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X39Y6          LUT6 (Prop_lut6_I1_O)        0.124     2.985 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.582     3.568    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X40Y8          LUT5 (Prop_lut5_I2_O)        0.124     3.692 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.436     5.128    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRC0
    SLICE_X42Y13         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.153     5.281 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMC/O
                         net (fo=2, routed)           0.803     6.084    Core_cpu/U_RF/rD20[4]
    SLICE_X41Y13         LUT6 (Prop_lut6_I0_O)        0.331     6.415 r  Core_cpu/U_RF/Mem_DRAM_i_42/O
                         net (fo=260, routed)         0.754     7.169    Core_cpu/U_RF/bbstub_spo[20][4]
    SLICE_X44Y9          LUT6 (Prop_lut6_I5_O)        0.124     7.293 r  Core_cpu/U_RF/C0_carry__0_i_10/O
                         net (fo=111, routed)         1.389     8.682    Core_cpu/U_RF/alu_B[4]
    SLICE_X58Y3          LUT5 (Prop_lut5_I3_O)        0.124     8.806 f  Core_cpu/U_RF/pc[1]_i_11/O
                         net (fo=2, routed)           0.509     9.315    Core_cpu/U_RF/pc[1]_i_11_n_0
    SLICE_X57Y3          LUT5 (Prop_lut5_I2_O)        0.124     9.439 f  Core_cpu/U_RF/pc[1]_i_7/O
                         net (fo=6, routed)           0.854    10.293    Core_cpu/U_RF/pc[1]_i_7_n_0
    SLICE_X60Y3          LUT5 (Prop_lut5_I4_O)        0.124    10.417 f  Core_cpu/U_RF/Mem_DRAM_i_162/O
                         net (fo=2, routed)           0.434    10.851    Core_cpu/U_RF/Mem_DRAM_i_162_n_0
    SLICE_X60Y4          LUT6 (Prop_lut6_I2_O)        0.124    10.975 r  Core_cpu/U_RF/Mem_DRAM_i_95/O
                         net (fo=1, routed)           1.236    12.211    Core_cpu/U_RF/Mem_DRAM_i_95_n_0
    SLICE_X53Y17         LUT6 (Prop_lut6_I4_O)        0.124    12.335 r  Core_cpu/U_RF/Mem_DRAM_i_13/O
                         net (fo=8202, routed)        4.776    17.111    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9/A1
    SLICE_X2Y120         RAMS64E (Prop_rams64e_ADR1_O)
                                                      1.440    18.551 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9/RAMS64E_A/O
                         net (fo=1, routed)           0.000    18.551    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9/OA
    SLICE_X2Y120         MUXF7 (Prop_muxf7_I1_O)      0.214    18.765 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9/F7.A/O
                         net (fo=1, routed)           0.000    18.765    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9/O1
    SLICE_X2Y120         MUXF8 (Prop_muxf8_I1_O)      0.088    18.853 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9/F8/O
                         net (fo=1, routed)           0.937    19.790    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9_n_0
    SLICE_X3Y114         LUT6 (Prop_lut6_I1_O)        0.319    20.109 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0_i_18/O
                         net (fo=1, routed)           0.000    20.109    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0_i_18_n_0
    SLICE_X3Y114         MUXF7 (Prop_muxf7_I1_O)      0.245    20.354 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0_i_7/O
                         net (fo=1, routed)           0.000    20.354    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0_i_7_n_0
    SLICE_X3Y114         MUXF8 (Prop_muxf8_I0_O)      0.104    20.458 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0_i_2/O
                         net (fo=1, routed)           1.373    21.831    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0_i_2_n_0
    SLICE_X13Y114        LUT6 (Prop_lut6_I1_O)        0.316    22.147 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0/O
                         net (fo=1, routed)           4.260    26.407    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1_2[9]
    SLICE_X36Y27         LUT3 (Prop_lut3_I1_O)        0.152    26.559 r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_18/O
                         net (fo=1, routed)           0.992    27.551    Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_18_n_0
    SLICE_X43Y17         LUT6 (Prop_lut6_I5_O)        0.332    27.883 r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_3/O
                         net (fo=2, routed)           0.828    28.711    Core_cpu/U_RF/registers_reg_r2_0_31_6_11/DIB1
    SLICE_X46Y13         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_6_11/RAMB_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.443    40.352    Core_cpu/U_RF/registers_reg_r2_0_31_6_11/WCLK
    SLICE_X46Y13         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_6_11/RAMB_D1/CLK
                         clock pessimism             -0.163    40.189    
                         clock uncertainty           -0.180    40.009    
    SLICE_X46Y13         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.228    39.781    Core_cpu/U_RF/registers_reg_r2_0_31_6_11/RAMB_D1
  -------------------------------------------------------------------
                         required time                         39.781    
                         arrival time                         -28.711    
  -------------------------------------------------------------------
                         slack                                 11.071    

Slack (MET) :             11.212ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r1_0_31_6_11/RAMB_D1/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        28.335ns  (logic 5.266ns (18.585%)  route 23.069ns (81.415%))
  Logic Levels:           20  (LUT3=1 LUT5=4 LUT6=9 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.045ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.353ns = ( 40.353 - 40.000 ) 
    Source Clock Delay      (SCD):    0.235ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.569     0.235    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X48Y8          FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y8          FDCE (Prop_fdce_C_Q)         0.456     0.691 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=56, routed)          1.613     2.304    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X39Y6          LUT6 (Prop_lut6_I0_O)        0.124     2.428 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     2.861    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X39Y6          LUT6 (Prop_lut6_I1_O)        0.124     2.985 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.582     3.568    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X40Y8          LUT5 (Prop_lut5_I2_O)        0.124     3.692 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.436     5.128    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRC0
    SLICE_X42Y13         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.153     5.281 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMC/O
                         net (fo=2, routed)           0.803     6.084    Core_cpu/U_RF/rD20[4]
    SLICE_X41Y13         LUT6 (Prop_lut6_I0_O)        0.331     6.415 r  Core_cpu/U_RF/Mem_DRAM_i_42/O
                         net (fo=260, routed)         0.754     7.169    Core_cpu/U_RF/bbstub_spo[20][4]
    SLICE_X44Y9          LUT6 (Prop_lut6_I5_O)        0.124     7.293 r  Core_cpu/U_RF/C0_carry__0_i_10/O
                         net (fo=111, routed)         1.389     8.682    Core_cpu/U_RF/alu_B[4]
    SLICE_X58Y3          LUT5 (Prop_lut5_I3_O)        0.124     8.806 f  Core_cpu/U_RF/pc[1]_i_11/O
                         net (fo=2, routed)           0.509     9.315    Core_cpu/U_RF/pc[1]_i_11_n_0
    SLICE_X57Y3          LUT5 (Prop_lut5_I2_O)        0.124     9.439 f  Core_cpu/U_RF/pc[1]_i_7/O
                         net (fo=6, routed)           0.854    10.293    Core_cpu/U_RF/pc[1]_i_7_n_0
    SLICE_X60Y3          LUT5 (Prop_lut5_I4_O)        0.124    10.417 f  Core_cpu/U_RF/Mem_DRAM_i_162/O
                         net (fo=2, routed)           0.434    10.851    Core_cpu/U_RF/Mem_DRAM_i_162_n_0
    SLICE_X60Y4          LUT6 (Prop_lut6_I2_O)        0.124    10.975 r  Core_cpu/U_RF/Mem_DRAM_i_95/O
                         net (fo=1, routed)           1.236    12.211    Core_cpu/U_RF/Mem_DRAM_i_95_n_0
    SLICE_X53Y17         LUT6 (Prop_lut6_I4_O)        0.124    12.335 r  Core_cpu/U_RF/Mem_DRAM_i_13/O
                         net (fo=8202, routed)        4.776    17.111    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9/A1
    SLICE_X2Y120         RAMS64E (Prop_rams64e_ADR1_O)
                                                      1.440    18.551 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9/RAMS64E_A/O
                         net (fo=1, routed)           0.000    18.551    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9/OA
    SLICE_X2Y120         MUXF7 (Prop_muxf7_I1_O)      0.214    18.765 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9/F7.A/O
                         net (fo=1, routed)           0.000    18.765    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9/O1
    SLICE_X2Y120         MUXF8 (Prop_muxf8_I1_O)      0.088    18.853 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9/F8/O
                         net (fo=1, routed)           0.937    19.790    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9728_9983_9_9_n_0
    SLICE_X3Y114         LUT6 (Prop_lut6_I1_O)        0.319    20.109 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0_i_18/O
                         net (fo=1, routed)           0.000    20.109    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0_i_18_n_0
    SLICE_X3Y114         MUXF7 (Prop_muxf7_I1_O)      0.245    20.354 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0_i_7/O
                         net (fo=1, routed)           0.000    20.354    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0_i_7_n_0
    SLICE_X3Y114         MUXF8 (Prop_muxf8_I0_O)      0.104    20.458 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0_i_2/O
                         net (fo=1, routed)           1.373    21.831    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0_i_2_n_0
    SLICE_X13Y114        LUT6 (Prop_lut6_I1_O)        0.316    22.147 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[9]_INST_0/O
                         net (fo=1, routed)           4.260    26.407    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1_2[9]
    SLICE_X36Y27         LUT3 (Prop_lut3_I1_O)        0.152    26.559 r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_18/O
                         net (fo=1, routed)           0.992    27.551    Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_18_n_0
    SLICE_X43Y17         LUT6 (Prop_lut6_I5_O)        0.332    27.883 r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_3/O
                         net (fo=2, routed)           0.687    28.570    Core_cpu/U_RF/registers_reg_r1_0_31_6_11/DIB1
    SLICE_X46Y12         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11/RAMB_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.444    40.353    Core_cpu/U_RF/registers_reg_r1_0_31_6_11/WCLK
    SLICE_X46Y12         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11/RAMB_D1/CLK
                         clock pessimism             -0.163    40.190    
                         clock uncertainty           -0.180    40.010    
    SLICE_X46Y12         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.228    39.782    Core_cpu/U_RF/registers_reg_r1_0_31_6_11/RAMB_D1
  -------------------------------------------------------------------
                         required time                         39.782    
                         arrival time                         -28.570    
  -------------------------------------------------------------------
                         slack                                 11.212    

Slack (MET) :             12.112ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r1_0_31_18_23/RAMC/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.494ns  (logic 3.640ns (13.239%)  route 23.854ns (86.761%))
  Logic Levels:           17  (LUT3=1 LUT4=2 LUT5=1 LUT6=9 MUXF7=1 MUXF8=2 RAMD32=1)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.359ns = ( 40.359 - 40.000 ) 
    Source Clock Delay      (SCD):    0.235ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.569     0.235    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X48Y8          FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y8          FDCE (Prop_fdce_C_Q)         0.456     0.691 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=56, routed)          1.613     2.304    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X39Y6          LUT6 (Prop_lut6_I0_O)        0.124     2.428 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     2.861    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X39Y6          LUT6 (Prop_lut6_I1_O)        0.124     2.985 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.582     3.568    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X40Y8          LUT5 (Prop_lut5_I2_O)        0.124     3.692 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.436     5.128    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRC0
    SLICE_X42Y13         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.153     5.281 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMC/O
                         net (fo=2, routed)           0.803     6.084    Core_cpu/U_RF/rD20[4]
    SLICE_X41Y13         LUT6 (Prop_lut6_I0_O)        0.331     6.415 r  Core_cpu/U_RF/Mem_DRAM_i_42/O
                         net (fo=260, routed)         0.754     7.169    Core_cpu/U_RF/bbstub_spo[20][4]
    SLICE_X44Y9          LUT6 (Prop_lut6_I5_O)        0.124     7.293 r  Core_cpu/U_RF/C0_carry__0_i_10/O
                         net (fo=111, routed)         1.307     8.600    Core_cpu/U_RF/alu_B[4]
    SLICE_X58Y4          LUT4 (Prop_lut4_I1_O)        0.124     8.724 f  Core_cpu/U_RF/Mem_DRAM_i_141/O
                         net (fo=24, routed)          1.059     9.782    Core_cpu/U_RF/Mem_DRAM_i_141_n_0
    SLICE_X62Y6          LUT4 (Prop_lut4_I2_O)        0.152     9.934 f  Core_cpu/U_RF/Mem_DRAM_i_127/O
                         net (fo=5, routed)           0.922    10.856    Core_cpu/U_RF/Mem_DRAM_i_127_n_0
    SLICE_X57Y6          LUT6 (Prop_lut6_I1_O)        0.332    11.188 f  Core_cpu/U_RF/Mem_DRAM_i_75/O
                         net (fo=1, routed)           0.889    12.077    Core_cpu/U_RF/Mem_DRAM_i_75_n_0
    SLICE_X51Y19         LUT6 (Prop_lut6_I5_O)        0.124    12.201 r  Core_cpu/U_RF/Mem_DRAM_i_7/O
                         net (fo=10246, routed)       6.328    18.529    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_22_22/A7
    SLICE_X34Y135        MUXF8 (Prop_muxf8_S_O)       0.283    18.812 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_22_22/F8/O
                         net (fo=1, routed)           1.076    19.888    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_22_22_n_0
    SLICE_X31Y127        LUT6 (Prop_lut6_I3_O)        0.319    20.207 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0_i_23/O
                         net (fo=1, routed)           0.000    20.207    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0_i_23_n_0
    SLICE_X31Y127        MUXF7 (Prop_muxf7_I0_O)      0.212    20.419 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0_i_10/O
                         net (fo=1, routed)           0.000    20.419    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0_i_10_n_0
    SLICE_X31Y127        MUXF8 (Prop_muxf8_I1_O)      0.094    20.513 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0_i_3/O
                         net (fo=1, routed)           1.134    21.647    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0_i_3_n_0
    SLICE_X39Y119        LUT6 (Prop_lut6_I3_O)        0.316    21.963 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0/O
                         net (fo=1, routed)           3.808    25.771    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1_2[22]
    SLICE_X39Y20         LUT3 (Prop_lut3_I1_O)        0.124    25.895 r  Core_cpu/U_RF/registers_reg_r1_0_31_18_23_i_23/O
                         net (fo=1, routed)           0.730    26.625    Core_cpu/U_RF/registers_reg_r1_0_31_18_23_i_23_n_0
    SLICE_X45Y20         LUT6 (Prop_lut6_I4_O)        0.124    26.749 r  Core_cpu/U_RF/registers_reg_r1_0_31_18_23_i_6/O
                         net (fo=2, routed)           0.980    27.729    Core_cpu/U_RF/registers_reg_r1_0_31_18_23/DIC0
    SLICE_X52Y10         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_18_23/RAMC/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.450    40.359    Core_cpu/U_RF/registers_reg_r1_0_31_18_23/WCLK
    SLICE_X52Y10         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_18_23/RAMC/CLK
                         clock pessimism             -0.163    40.196    
                         clock uncertainty           -0.180    40.016    
    SLICE_X52Y10         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.175    39.841    Core_cpu/U_RF/registers_reg_r1_0_31_18_23/RAMC
  -------------------------------------------------------------------
                         required time                         39.841    
                         arrival time                         -27.729    
  -------------------------------------------------------------------
                         slack                                 12.112    

Slack (MET) :             12.126ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r2_0_31_18_23/RAMC/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.480ns  (logic 3.640ns (13.246%)  route 23.840ns (86.754%))
  Logic Levels:           17  (LUT3=1 LUT4=2 LUT5=1 LUT6=9 MUXF7=1 MUXF8=2 RAMD32=1)
  Clock Path Skew:        -0.040ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.358ns = ( 40.358 - 40.000 ) 
    Source Clock Delay      (SCD):    0.235ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.569     0.235    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X48Y8          FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y8          FDCE (Prop_fdce_C_Q)         0.456     0.691 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=56, routed)          1.613     2.304    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X39Y6          LUT6 (Prop_lut6_I0_O)        0.124     2.428 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     2.861    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X39Y6          LUT6 (Prop_lut6_I1_O)        0.124     2.985 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.582     3.568    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X40Y8          LUT5 (Prop_lut5_I2_O)        0.124     3.692 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.436     5.128    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRC0
    SLICE_X42Y13         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.153     5.281 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMC/O
                         net (fo=2, routed)           0.803     6.084    Core_cpu/U_RF/rD20[4]
    SLICE_X41Y13         LUT6 (Prop_lut6_I0_O)        0.331     6.415 r  Core_cpu/U_RF/Mem_DRAM_i_42/O
                         net (fo=260, routed)         0.754     7.169    Core_cpu/U_RF/bbstub_spo[20][4]
    SLICE_X44Y9          LUT6 (Prop_lut6_I5_O)        0.124     7.293 r  Core_cpu/U_RF/C0_carry__0_i_10/O
                         net (fo=111, routed)         1.307     8.600    Core_cpu/U_RF/alu_B[4]
    SLICE_X58Y4          LUT4 (Prop_lut4_I1_O)        0.124     8.724 f  Core_cpu/U_RF/Mem_DRAM_i_141/O
                         net (fo=24, routed)          1.059     9.782    Core_cpu/U_RF/Mem_DRAM_i_141_n_0
    SLICE_X62Y6          LUT4 (Prop_lut4_I2_O)        0.152     9.934 f  Core_cpu/U_RF/Mem_DRAM_i_127/O
                         net (fo=5, routed)           0.922    10.856    Core_cpu/U_RF/Mem_DRAM_i_127_n_0
    SLICE_X57Y6          LUT6 (Prop_lut6_I1_O)        0.332    11.188 f  Core_cpu/U_RF/Mem_DRAM_i_75/O
                         net (fo=1, routed)           0.889    12.077    Core_cpu/U_RF/Mem_DRAM_i_75_n_0
    SLICE_X51Y19         LUT6 (Prop_lut6_I5_O)        0.124    12.201 r  Core_cpu/U_RF/Mem_DRAM_i_7/O
                         net (fo=10246, routed)       6.328    18.529    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_22_22/A7
    SLICE_X34Y135        MUXF8 (Prop_muxf8_S_O)       0.283    18.812 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_22_22/F8/O
                         net (fo=1, routed)           1.076    19.888    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_22_22_n_0
    SLICE_X31Y127        LUT6 (Prop_lut6_I3_O)        0.319    20.207 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0_i_23/O
                         net (fo=1, routed)           0.000    20.207    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0_i_23_n_0
    SLICE_X31Y127        MUXF7 (Prop_muxf7_I0_O)      0.212    20.419 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0_i_10/O
                         net (fo=1, routed)           0.000    20.419    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0_i_10_n_0
    SLICE_X31Y127        MUXF8 (Prop_muxf8_I1_O)      0.094    20.513 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0_i_3/O
                         net (fo=1, routed)           1.134    21.647    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0_i_3_n_0
    SLICE_X39Y119        LUT6 (Prop_lut6_I3_O)        0.316    21.963 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[22]_INST_0/O
                         net (fo=1, routed)           3.808    25.771    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1_2[22]
    SLICE_X39Y20         LUT3 (Prop_lut3_I1_O)        0.124    25.895 r  Core_cpu/U_RF/registers_reg_r1_0_31_18_23_i_23/O
                         net (fo=1, routed)           0.730    26.625    Core_cpu/U_RF/registers_reg_r1_0_31_18_23_i_23_n_0
    SLICE_X45Y20         LUT6 (Prop_lut6_I4_O)        0.124    26.749 r  Core_cpu/U_RF/registers_reg_r1_0_31_18_23_i_6/O
                         net (fo=2, routed)           0.966    27.715    Core_cpu/U_RF/registers_reg_r2_0_31_18_23/DIC0
    SLICE_X52Y11         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_18_23/RAMC/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.449    40.358    Core_cpu/U_RF/registers_reg_r2_0_31_18_23/WCLK
    SLICE_X52Y11         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_18_23/RAMC/CLK
                         clock pessimism             -0.163    40.195    
                         clock uncertainty           -0.180    40.015    
    SLICE_X52Y11         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.175    39.840    Core_cpu/U_RF/registers_reg_r2_0_31_18_23/RAMC
  -------------------------------------------------------------------
                         required time                         39.840    
                         arrival time                         -27.715    
  -------------------------------------------------------------------
                         slack                                 12.126    

Slack (MET) :             12.388ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r2_0_31_30_31/RAMA_D1/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.136ns  (logic 3.584ns (13.208%)  route 23.552ns (86.792%))
  Logic Levels:           19  (LUT2=1 LUT3=1 LUT5=1 LUT6=10 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.038ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.360ns = ( 40.360 - 40.000 ) 
    Source Clock Delay      (SCD):    0.235ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.569     0.235    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X48Y8          FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y8          FDCE (Prop_fdce_C_Q)         0.456     0.691 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=56, routed)          1.613     2.304    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X39Y6          LUT6 (Prop_lut6_I0_O)        0.124     2.428 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     2.861    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X39Y6          LUT6 (Prop_lut6_I1_O)        0.124     2.985 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.582     3.568    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X40Y8          LUT5 (Prop_lut5_I2_O)        0.124     3.692 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.436     5.128    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRC0
    SLICE_X42Y13         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.153     5.281 f  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMC/O
                         net (fo=2, routed)           0.803     6.084    Core_cpu/U_RF/rD20[4]
    SLICE_X41Y13         LUT6 (Prop_lut6_I0_O)        0.331     6.415 f  Core_cpu/U_RF/Mem_DRAM_i_42/O
                         net (fo=260, routed)         0.754     7.169    Core_cpu/U_RF/bbstub_spo[20][4]
    SLICE_X44Y9          LUT6 (Prop_lut6_I5_O)        0.124     7.293 f  Core_cpu/U_RF/C0_carry__0_i_10/O
                         net (fo=111, routed)         1.420     8.713    Core_cpu/U_RF/alu_B[4]
    SLICE_X57Y4          LUT2 (Prop_lut2_I1_O)        0.124     8.837 f  Core_cpu/U_RF/pc[29]_i_9/O
                         net (fo=23, routed)          0.654     9.491    Core_cpu/U_RF/pc[29]_i_9_n_0
    SLICE_X53Y3          LUT6 (Prop_lut6_I2_O)        0.124     9.615 f  Core_cpu/U_RF/Mem_DRAM_i_140/O
                         net (fo=2, routed)           0.716    10.331    Core_cpu/U_RF/Mem_DRAM_i_140_n_0
    SLICE_X58Y4          LUT6 (Prop_lut6_I4_O)        0.124    10.455 f  Core_cpu/U_RF/Mem_DRAM_i_81/O
                         net (fo=1, routed)           1.195    11.650    Core_cpu/U_RF/Mem_DRAM_i_81_n_0
    SLICE_X55Y6          LUT6 (Prop_lut6_I2_O)        0.124    11.774 r  Core_cpu/U_RF/Mem_DRAM_i_9/O
                         net (fo=8197, routed)        6.541    18.315    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6912_7167_31_31/A5
    SLICE_X6Y113         RAMS64E (Prop_rams64e_ADR5_O)
                                                      0.124    18.439 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6912_7167_31_31/RAMS64E_D/O
                         net (fo=1, routed)           0.000    18.439    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6912_7167_31_31/OD
    SLICE_X6Y113         MUXF7 (Prop_muxf7_I0_O)      0.241    18.680 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6912_7167_31_31/F7.B/O
                         net (fo=1, routed)           0.000    18.680    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6912_7167_31_31/O0
    SLICE_X6Y113         MUXF8 (Prop_muxf8_I0_O)      0.098    18.778 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6912_7167_31_31/F8/O
                         net (fo=1, routed)           0.971    19.749    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6912_7167_31_31_n_0
    SLICE_X7Y107         LUT6 (Prop_lut6_I0_O)        0.319    20.068 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_23/O
                         net (fo=1, routed)           0.000    20.068    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_23_n_0
    SLICE_X7Y107         MUXF7 (Prop_muxf7_I0_O)      0.212    20.280 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_10/O
                         net (fo=1, routed)           0.000    20.280    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_10_n_0
    SLICE_X7Y107         MUXF8 (Prop_muxf8_I1_O)      0.094    20.374 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_3/O
                         net (fo=1, routed)           1.473    21.847    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_3_n_0
    SLICE_X13Y96         LUT6 (Prop_lut6_I3_O)        0.316    22.163 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0/O
                         net (fo=1, routed)           3.082    25.245    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1_2[31]
    SLICE_X36Y27         LUT3 (Prop_lut3_I1_O)        0.124    25.369 r  Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_5/O
                         net (fo=1, routed)           0.705    26.074    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_5_n_0
    SLICE_X45Y19         LUT6 (Prop_lut6_I5_O)        0.124    26.198 r  Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1/O
                         net (fo=2, routed)           1.173    27.371    Core_cpu/U_RF/registers_reg_r2_0_31_30_31/DIA1
    SLICE_X52Y8          RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_30_31/RAMA_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.451    40.360    Core_cpu/U_RF/registers_reg_r2_0_31_30_31/WCLK
    SLICE_X52Y8          RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_30_31/RAMA_D1/CLK
                         clock pessimism             -0.163    40.197    
                         clock uncertainty           -0.180    40.017    
    SLICE_X52Y8          RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.258    39.759    Core_cpu/U_RF/registers_reg_r2_0_31_30_31/RAMA_D1
  -------------------------------------------------------------------
                         required time                         39.759    
                         arrival time                         -27.371    
  -------------------------------------------------------------------
                         slack                                 12.388    

Slack (MET) :             12.472ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r2_0_31_6_11/RAMC/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.127ns  (logic 3.836ns (14.141%)  route 23.291ns (85.859%))
  Logic Levels:           17  (LUT3=1 LUT4=2 LUT5=1 LUT6=9 MUXF7=1 MUXF8=2 RAMD32=1)
  Clock Path Skew:        -0.046ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.352ns = ( 40.352 - 40.000 ) 
    Source Clock Delay      (SCD):    0.235ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.569     0.235    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X48Y8          FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y8          FDCE (Prop_fdce_C_Q)         0.456     0.691 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=56, routed)          1.613     2.304    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X39Y6          LUT6 (Prop_lut6_I0_O)        0.124     2.428 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     2.861    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X39Y6          LUT6 (Prop_lut6_I1_O)        0.124     2.985 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.582     3.568    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X40Y8          LUT5 (Prop_lut5_I2_O)        0.124     3.692 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.436     5.128    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRC0
    SLICE_X42Y13         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.153     5.281 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMC/O
                         net (fo=2, routed)           0.803     6.084    Core_cpu/U_RF/rD20[4]
    SLICE_X41Y13         LUT6 (Prop_lut6_I0_O)        0.331     6.415 r  Core_cpu/U_RF/Mem_DRAM_i_42/O
                         net (fo=260, routed)         0.754     7.169    Core_cpu/U_RF/bbstub_spo[20][4]
    SLICE_X44Y9          LUT6 (Prop_lut6_I5_O)        0.124     7.293 r  Core_cpu/U_RF/C0_carry__0_i_10/O
                         net (fo=111, routed)         1.307     8.600    Core_cpu/U_RF/alu_B[4]
    SLICE_X58Y4          LUT4 (Prop_lut4_I1_O)        0.124     8.724 f  Core_cpu/U_RF/Mem_DRAM_i_141/O
                         net (fo=24, routed)          1.059     9.782    Core_cpu/U_RF/Mem_DRAM_i_141_n_0
    SLICE_X62Y6          LUT4 (Prop_lut4_I2_O)        0.152     9.934 f  Core_cpu/U_RF/Mem_DRAM_i_127/O
                         net (fo=5, routed)           0.922    10.856    Core_cpu/U_RF/Mem_DRAM_i_127_n_0
    SLICE_X57Y6          LUT6 (Prop_lut6_I1_O)        0.332    11.188 f  Core_cpu/U_RF/Mem_DRAM_i_75/O
                         net (fo=1, routed)           0.889    12.077    Core_cpu/U_RF/Mem_DRAM_i_75_n_0
    SLICE_X51Y19         LUT6 (Prop_lut6_I5_O)        0.124    12.201 r  Core_cpu/U_RF/Mem_DRAM_i_7/O
                         net (fo=10246, routed)       5.686    17.888    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_10_10/A7
    SLICE_X30Y127        MUXF8 (Prop_muxf8_S_O)       0.283    18.171 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_10_10/F8/O
                         net (fo=1, routed)           1.148    19.319    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10752_11007_10_10_n_0
    SLICE_X31Y118        LUT6 (Prop_lut6_I1_O)        0.319    19.638 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[10]_INST_0_i_19/O
                         net (fo=1, routed)           0.000    19.638    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[10]_INST_0_i_19_n_0
    SLICE_X31Y118        MUXF7 (Prop_muxf7_I0_O)      0.212    19.850 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[10]_INST_0_i_8/O
                         net (fo=1, routed)           0.000    19.850    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[10]_INST_0_i_8_n_0
    SLICE_X31Y118        MUXF8 (Prop_muxf8_I1_O)      0.094    19.944 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[10]_INST_0_i_2/O
                         net (fo=1, routed)           2.124    22.067    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[10]_INST_0_i_2_n_0
    SLICE_X55Y97         LUT6 (Prop_lut6_I1_O)        0.316    22.383 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[10]_INST_0/O
                         net (fo=1, routed)           2.978    25.362    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1_2[10]
    SLICE_X47Y29         LUT3 (Prop_lut3_I1_O)        0.118    25.480 r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_30/O
                         net (fo=1, routed)           0.933    26.413    Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_30_n_0
    SLICE_X47Y18         LUT6 (Prop_lut6_I5_O)        0.326    26.739 r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_6/O
                         net (fo=2, routed)           0.624    27.362    Core_cpu/U_RF/registers_reg_r2_0_31_6_11/DIC0
    SLICE_X46Y13         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_6_11/RAMC/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.443    40.352    Core_cpu/U_RF/registers_reg_r2_0_31_6_11/WCLK
    SLICE_X46Y13         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_6_11/RAMC/CLK
                         clock pessimism             -0.163    40.189    
                         clock uncertainty           -0.180    40.009    
    SLICE_X46Y13         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.175    39.834    Core_cpu/U_RF/registers_reg_r2_0_31_6_11/RAMC
  -------------------------------------------------------------------
                         required time                         39.834    
                         arrival time                         -27.362    
  -------------------------------------------------------------------
                         slack                                 12.472    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.218ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][7]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.361ns  (logic 0.190ns (52.691%)  route 0.171ns (47.309%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.036ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.993ns
    Source Clock Delay      (SCD):    0.436ns
    Clock Pessimism Removal (CPR):    0.521ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.585     0.436    U_Button/cpu_clk_BUFG
    SLICE_X61Y20         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X61Y20         FDCE (Prop_fdce_C_Q)         0.141     0.577 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.171     0.748    U_Button/p_1_in8_in
    SLICE_X63Y20         LUT4 (Prop_lut4_I2_O)        0.049     0.797 r  U_Button/debounce_gen[1].debounce_cnt[1][7]_i_1/O
                         net (fo=1, routed)           0.000     0.797    U_Button/debounce_gen[1].debounce_cnt[1][7]_i_1_n_0
    SLICE_X63Y20         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][7]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.855     0.993    U_Button/cpu_clk_BUFG
    SLICE_X63Y20         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][7]/C
                         clock pessimism             -0.521     0.472    
    SLICE_X63Y20         FDCE (Hold_fdce_C_D)         0.107     0.579    U_Button/debounce_gen[1].debounce_cnt_reg[1][7]
  -------------------------------------------------------------------
                         required time                         -0.579    
                         arrival time                           0.797    
  -------------------------------------------------------------------
                         slack                                  0.218    

Slack (MET) :             0.229ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][6]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.357ns  (logic 0.186ns (52.161%)  route 0.171ns (47.839%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.036ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.993ns
    Source Clock Delay      (SCD):    0.436ns
    Clock Pessimism Removal (CPR):    0.521ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.585     0.436    U_Button/cpu_clk_BUFG
    SLICE_X61Y20         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X61Y20         FDCE (Prop_fdce_C_Q)         0.141     0.577 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.171     0.748    U_Button/p_1_in8_in
    SLICE_X63Y20         LUT4 (Prop_lut4_I2_O)        0.045     0.793 r  U_Button/debounce_gen[1].debounce_cnt[1][6]_i_1/O
                         net (fo=1, routed)           0.000     0.793    U_Button/debounce_gen[1].debounce_cnt[1][6]_i_1_n_0
    SLICE_X63Y20         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][6]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.855     0.993    U_Button/cpu_clk_BUFG
    SLICE_X63Y20         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][6]/C
                         clock pessimism             -0.521     0.472    
    SLICE_X63Y20         FDCE (Hold_fdce_C_D)         0.092     0.564    U_Button/debounce_gen[1].debounce_cnt_reg[1][6]
  -------------------------------------------------------------------
                         required time                         -0.564    
                         arrival time                           0.793    
  -------------------------------------------------------------------
                         slack                                  0.229    

Slack (MET) :             0.230ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[3].debounce_cnt_reg[3][0]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.334ns  (logic 0.186ns (55.649%)  route 0.148ns (44.351%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.986ns
    Source Clock Delay      (SCD):    0.432ns
    Clock Pessimism Removal (CPR):    0.541ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.581     0.432    U_Button/cpu_clk_BUFG
    SLICE_X61Y24         FDCE                                         r  U_Button/button_sync2_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X61Y24         FDCE (Prop_fdce_C_Q)         0.141     0.573 r  U_Button/button_sync2_reg[3]/Q
                         net (fo=21, routed)          0.148     0.721    U_Button/p_1_in2_in
    SLICE_X59Y24         LUT4 (Prop_lut4_I2_O)        0.045     0.766 r  U_Button/debounce_gen[3].debounce_cnt[3][0]_i_1/O
                         net (fo=1, routed)           0.000     0.766    U_Button/debounce_gen[3].debounce_cnt[3][0]_i_1_n_0
    SLICE_X59Y24         FDCE                                         r  U_Button/debounce_gen[3].debounce_cnt_reg[3][0]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.848     0.986    U_Button/cpu_clk_BUFG
    SLICE_X59Y24         FDCE                                         r  U_Button/debounce_gen[3].debounce_cnt_reg[3][0]/C
                         clock pessimism             -0.541     0.445    
    SLICE_X59Y24         FDCE (Hold_fdce_C_D)         0.091     0.536    U_Button/debounce_gen[3].debounce_cnt_reg[3][0]
  -------------------------------------------------------------------
                         required time                         -0.536    
                         arrival time                           0.766    
  -------------------------------------------------------------------
                         slack                                  0.230    

Slack (MET) :             0.245ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[0].debounce_cnt_reg[0][13]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.413ns  (logic 0.189ns (45.770%)  route 0.224ns (54.230%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.037ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.995ns
    Source Clock Delay      (SCD):    0.437ns
    Clock Pessimism Removal (CPR):    0.521ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.586     0.437    U_Button/cpu_clk_BUFG
    SLICE_X61Y30         FDCE                                         r  U_Button/button_sync2_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X61Y30         FDCE (Prop_fdce_C_Q)         0.141     0.578 r  U_Button/button_sync2_reg[0]/Q
                         net (fo=21, routed)          0.224     0.802    U_Button/button_sync2_reg_n_0_[0]
    SLICE_X64Y31         LUT4 (Prop_lut4_I2_O)        0.048     0.850 r  U_Button/debounce_gen[0].debounce_cnt[0][13]_i_1/O
                         net (fo=1, routed)           0.000     0.850    U_Button/debounce_gen[0].debounce_cnt[0][13]_i_1_n_0
    SLICE_X64Y31         FDCE                                         r  U_Button/debounce_gen[0].debounce_cnt_reg[0][13]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.857     0.995    U_Button/cpu_clk_BUFG
    SLICE_X64Y31         FDCE                                         r  U_Button/debounce_gen[0].debounce_cnt_reg[0][13]/C
                         clock pessimism             -0.521     0.474    
    SLICE_X64Y31         FDCE (Hold_fdce_C_D)         0.131     0.605    U_Button/debounce_gen[0].debounce_cnt_reg[0][13]
  -------------------------------------------------------------------
                         required time                         -0.605    
                         arrival time                           0.850    
  -------------------------------------------------------------------
                         slack                                  0.245    

Slack (MET) :             0.246ns  (arrival time - required time)
  Source:                 U_Dig/scan_cnt_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Dig/scan_cnt_reg[2]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.353ns  (logic 0.183ns (51.772%)  route 0.170ns (48.228%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.968ns
    Source Clock Delay      (SCD):    0.412ns
    Clock Pessimism Removal (CPR):    0.556ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.561     0.412    U_Dig/cpu_clk_BUFG
    SLICE_X33Y39         FDCE                                         r  U_Dig/scan_cnt_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X33Y39         FDCE (Prop_fdce_C_Q)         0.141     0.553 r  U_Dig/scan_cnt_reg[1]/Q
                         net (fo=18, routed)          0.170     0.724    U_Dig/scan_cnt[1]
    SLICE_X33Y39         LUT4 (Prop_lut4_I1_O)        0.042     0.766 r  U_Dig/scan_cnt[2]_i_1/O
                         net (fo=1, routed)           0.000     0.766    U_Dig/scan_cnt[2]_i_1_n_0
    SLICE_X33Y39         FDCE                                         r  U_Dig/scan_cnt_reg[2]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.830     0.968    U_Dig/cpu_clk_BUFG
    SLICE_X33Y39         FDCE                                         r  U_Dig/scan_cnt_reg[2]/C
                         clock pessimism             -0.556     0.412    
    SLICE_X33Y39         FDCE (Hold_fdce_C_D)         0.107     0.519    U_Dig/scan_cnt_reg[2]
  -------------------------------------------------------------------
                         required time                         -0.519    
                         arrival time                           0.766    
  -------------------------------------------------------------------
                         slack                                  0.246    

Slack (MET) :             0.253ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[0].debounce_cnt_reg[0][10]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.410ns  (logic 0.186ns (45.373%)  route 0.224ns (54.627%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.037ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.995ns
    Source Clock Delay      (SCD):    0.437ns
    Clock Pessimism Removal (CPR):    0.521ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.586     0.437    U_Button/cpu_clk_BUFG
    SLICE_X61Y30         FDCE                                         r  U_Button/button_sync2_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X61Y30         FDCE (Prop_fdce_C_Q)         0.141     0.578 r  U_Button/button_sync2_reg[0]/Q
                         net (fo=21, routed)          0.224     0.802    U_Button/button_sync2_reg_n_0_[0]
    SLICE_X64Y31         LUT4 (Prop_lut4_I2_O)        0.045     0.847 r  U_Button/debounce_gen[0].debounce_cnt[0][10]_i_1/O
                         net (fo=1, routed)           0.000     0.847    U_Button/debounce_gen[0].debounce_cnt[0][10]_i_1_n_0
    SLICE_X64Y31         FDCE                                         r  U_Button/debounce_gen[0].debounce_cnt_reg[0][10]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.857     0.995    U_Button/cpu_clk_BUFG
    SLICE_X64Y31         FDCE                                         r  U_Button/debounce_gen[0].debounce_cnt_reg[0][10]/C
                         clock pessimism             -0.521     0.474    
    SLICE_X64Y31         FDCE (Hold_fdce_C_D)         0.120     0.594    U_Button/debounce_gen[0].debounce_cnt_reg[0][10]
  -------------------------------------------------------------------
                         required time                         -0.594    
                         arrival time                           0.847    
  -------------------------------------------------------------------
                         slack                                  0.253    

Slack (MET) :             0.263ns  (arrival time - required time)
  Source:                 Core_cpu/U_PC/pc_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_PC/pc_reg[1]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.354ns  (logic 0.186ns (52.507%)  route 0.168ns (47.493%))
  Logic Levels:           1  (LUT5=1)
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.971ns
    Source Clock Delay      (SCD):    0.414ns
    Clock Pessimism Removal (CPR):    0.557ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.563     0.414    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X47Y9          FDCE                                         r  Core_cpu/U_PC/pc_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y9          FDCE (Prop_fdce_C_Q)         0.141     0.555 r  Core_cpu/U_PC/pc_reg[1]/Q
                         net (fo=5, routed)           0.168     0.723    Core_cpu/U_PC/Q[1]
    SLICE_X47Y9          LUT5 (Prop_lut5_I2_O)        0.045     0.768 r  Core_cpu/U_PC/pc[1]_i_1/O
                         net (fo=1, routed)           0.000     0.768    Core_cpu/U_PC/pc_next[1]
    SLICE_X47Y9          FDCE                                         r  Core_cpu/U_PC/pc_reg[1]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.833     0.971    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X47Y9          FDCE                                         r  Core_cpu/U_PC/pc_reg[1]/C
                         clock pessimism             -0.557     0.414    
    SLICE_X47Y9          FDCE (Hold_fdce_C_D)         0.091     0.505    Core_cpu/U_PC/pc_reg[1]
  -------------------------------------------------------------------
                         required time                         -0.505    
                         arrival time                           0.768    
  -------------------------------------------------------------------
                         slack                                  0.263    

Slack (MET) :             0.263ns  (arrival time - required time)
  Source:                 U_Button/debounce_gen[1].button_stable_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][5]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.383ns  (logic 0.189ns (49.298%)  route 0.194ns (50.702%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.993ns
    Source Clock Delay      (SCD):    0.438ns
    Clock Pessimism Removal (CPR):    0.542ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.587     0.438    U_Button/cpu_clk_BUFG
    SLICE_X63Y19         FDCE                                         r  U_Button/debounce_gen[1].button_stable_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X63Y19         FDCE (Prop_fdce_C_Q)         0.141     0.579 r  U_Button/debounce_gen[1].button_stable_reg[1]/Q
                         net (fo=22, routed)          0.194     0.774    U_Button/data0[1]
    SLICE_X63Y20         LUT4 (Prop_lut4_I1_O)        0.048     0.822 r  U_Button/debounce_gen[1].debounce_cnt[1][5]_i_1/O
                         net (fo=1, routed)           0.000     0.822    U_Button/debounce_gen[1].debounce_cnt[1][5]_i_1_n_0
    SLICE_X63Y20         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][5]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.855     0.993    U_Button/cpu_clk_BUFG
    SLICE_X63Y20         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][5]/C
                         clock pessimism             -0.542     0.451    
    SLICE_X63Y20         FDCE (Hold_fdce_C_D)         0.107     0.558    U_Button/debounce_gen[1].debounce_cnt_reg[1][5]
  -------------------------------------------------------------------
                         required time                         -0.558    
                         arrival time                           0.822    
  -------------------------------------------------------------------
                         slack                                  0.263    

Slack (MET) :             0.265ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][1]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.394ns  (logic 0.186ns (47.244%)  route 0.208ns (52.756%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.037ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.994ns
    Source Clock Delay      (SCD):    0.436ns
    Clock Pessimism Removal (CPR):    0.521ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.585     0.436    U_Button/cpu_clk_BUFG
    SLICE_X61Y20         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X61Y20         FDCE (Prop_fdce_C_Q)         0.141     0.577 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.208     0.785    U_Button/p_1_in8_in
    SLICE_X63Y19         LUT4 (Prop_lut4_I2_O)        0.045     0.830 r  U_Button/debounce_gen[1].debounce_cnt[1][1]_i_1/O
                         net (fo=1, routed)           0.000     0.830    U_Button/debounce_gen[1].debounce_cnt[1][1]_i_1_n_0
    SLICE_X63Y19         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][1]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.856     0.994    U_Button/cpu_clk_BUFG
    SLICE_X63Y19         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][1]/C
                         clock pessimism             -0.521     0.473    
    SLICE_X63Y19         FDCE (Hold_fdce_C_D)         0.092     0.565    U_Button/debounce_gen[1].debounce_cnt_reg[1][1]
  -------------------------------------------------------------------
                         required time                         -0.565    
                         arrival time                           0.830    
  -------------------------------------------------------------------
                         slack                                  0.265    

Slack (MET) :             0.265ns  (arrival time - required time)
  Source:                 U_Dig/scan_cnt_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Dig/scan_cnt_reg[1]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.356ns  (logic 0.186ns (52.178%)  route 0.170ns (47.822%))
  Logic Levels:           1  (LUT3=1)
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.968ns
    Source Clock Delay      (SCD):    0.412ns
    Clock Pessimism Removal (CPR):    0.556ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.561     0.412    U_Dig/cpu_clk_BUFG
    SLICE_X33Y39         FDCE                                         r  U_Dig/scan_cnt_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X33Y39         FDCE (Prop_fdce_C_Q)         0.141     0.553 r  U_Dig/scan_cnt_reg[1]/Q
                         net (fo=18, routed)          0.170     0.724    U_Dig/scan_cnt[1]
    SLICE_X33Y39         LUT3 (Prop_lut3_I2_O)        0.045     0.769 r  U_Dig/scan_cnt[1]_i_1/O
                         net (fo=1, routed)           0.000     0.769    U_Dig/scan_cnt[1]_i_1_n_0
    SLICE_X33Y39         FDCE                                         r  U_Dig/scan_cnt_reg[1]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I1_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.830     0.968    U_Dig/cpu_clk_BUFG
    SLICE_X33Y39         FDCE                                         r  U_Dig/scan_cnt_reg[1]/C
                         clock pessimism             -0.556     0.412    
    SLICE_X33Y39         FDCE (Hold_fdce_C_D)         0.091     0.503    U_Dig/scan_cnt_reg[1]
  -------------------------------------------------------------------
                         required time                         -0.503    
                         arrival time                           0.769    
  -------------------------------------------------------------------
                         slack                                  0.265    





Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clk_out1_cpuclk
Waveform(ns):       { 0.000 20.000 }
Period(ns):         40.000
Sources:            { Clkgen/inst/plle2_adv_inst/CLKOUT0 }

Check Type        Corner  Lib Pin            Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     BUFG/I             n/a            2.155         40.000      37.845     BUFGCTRL_X0Y2   Clkgen/inst/clkout1_buf/I
Min Period        n/a     BUFG/I             n/a            2.155         40.000      37.845     BUFGCTRL_X0Y0   cpu_clk_BUFG_inst/I
Min Period        n/a     PLLE2_ADV/CLKOUT0  n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKOUT0
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X45Y9     Core_cpu/U_PC/pc_reg[0]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X50Y14    Core_cpu/U_PC/pc_reg[10]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X50Y14    Core_cpu/U_PC/pc_reg[11]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X52Y15    Core_cpu/U_PC/pc_reg[12]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X51Y14    Core_cpu/U_PC/pc_reg[13]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X51Y15    Core_cpu/U_PC/pc_reg[14]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X51Y15    Core_cpu/U_PC/pc_reg[15]/C
Max Period        n/a     PLLE2_ADV/CLKOUT0  n/a            160.000       40.000      120.000    PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKOUT0
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X34Y80    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_11_11/RAMS64E_A/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X34Y80    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_11_11/RAMS64E_B/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y20    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_3_3/RAMS64E_A/CLK
Low Pulse Width   Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X6Y34     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_4_4/RAMS64E_B/CLK
Low Pulse Width   Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X6Y34     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_4_4/RAMS64E_C/CLK
Low Pulse Width   Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X6Y34     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_4_4/RAMS64E_D/CLK
Low Pulse Width   Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X64Y15    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_5_5/RAMS64E_A/CLK
Low Pulse Width   Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X64Y15    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_5_5/RAMS64E_B/CLK
Low Pulse Width   Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X64Y15    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_5_5/RAMS64E_C/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X54Y48    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_3584_3839_26_26/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X2Y132    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_29_29/RAMS64E_D/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X10Y59    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_2_2/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X10Y59    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_2_2/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X10Y59    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_2_2/RAMS64E_C/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X10Y59    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_2_2/RAMS64E_D/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X34Y120   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_10_10/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X34Y120   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_10_10/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X34Y120   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_10_10/RAMS64E_C/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X34Y120   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_10_10/RAMS64E_D/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X34Y80    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_11_11/RAMS64E_A/CLK



---------------------------------------------------------------------------------------------------
From Clock:  clkfbout_cpuclk
  To Clock:  clkfbout_cpuclk

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack       12.633ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clkfbout_cpuclk
Waveform(ns):       { 0.000 20.000 }
Period(ns):         40.000
Sources:            { Clkgen/inst/plle2_adv_inst/CLKFBOUT }

Check Type  Corner  Lib Pin             Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period  n/a     BUFG/I              n/a            2.155         40.000      37.845     BUFGCTRL_X0Y1   Clkgen/inst/clkf_buf/I
Min Period  n/a     PLLE2_ADV/CLKFBOUT  n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBOUT
Min Period  n/a     PLLE2_ADV/CLKFBIN   n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBIN
Max Period  n/a     PLLE2_ADV/CLKFBIN   n/a            52.633        40.000      12.633     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBIN
Max Period  n/a     PLLE2_ADV/CLKFBOUT  n/a            160.000       40.000      120.000    PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBOUT



