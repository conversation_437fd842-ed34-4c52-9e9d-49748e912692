Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Thu Jul  3 18:34:35 2025
| Host         : L running 64-bit major release  (build 9200)
| Command      : report_timing_summary -max_paths 10 -file miniRV_SoC_timing_summary_routed.rpt -pb miniRV_SoC_timing_summary_routed.pb -rpx miniRV_SoC_timing_summary_routed.rpx -warn_on_violation
| Design       : miniRV_SoC
| Device       : 7a35t-csg324
| Speed File   : -1  PRODUCTION 1.23 2018-06-13
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Timing Summary Report

------------------------------------------------------------------------------------------------
| Timer Settings
| --------------
------------------------------------------------------------------------------------------------

  Enable Multi Corner Analysis               :  Yes
  Enable Pessimism Removal                   :  Yes
  Pessimism Removal Resolution               :  Nearest Common Node
  Enable Input Delay Default Clock           :  No
  Enable Preset / Clear Arcs                 :  No
  Disable Flight Delays                      :  No
  Ignore I/O Paths                           :  No
  Timing Early Launch at Borrowing Latches   :  false

  Corner  Analyze    Analyze    
  Name    Max Paths  Min Paths  
  ------  ---------  ---------  
  Slow    Yes        Yes        
  Fast    Yes        Yes        



check_timing report

Table of Contents
-----------------
1. checking no_clock
2. checking constant_clock
3. checking pulse_width_clock
4. checking unconstrained_internal_endpoints
5. checking no_input_delay
6. checking no_output_delay
7. checking multiple_clock
8. checking generated_clocks
9. checking loops
10. checking partial_input_delay
11. checking partial_output_delay
12. checking latch_loops

1. checking no_clock
--------------------
 There are 0 register/latch pins with no clock.


2. checking constant_clock
--------------------------
 There are 0 register/latch pins with constant_clock.


3. checking pulse_width_clock
-----------------------------
 There are 0 register/latch pins which need pulse_width check


4. checking unconstrained_internal_endpoints
--------------------------------------------
 There are 0 pins that are not constrained for maximum delay.

 There are 0 pins that are not constrained for maximum delay due to constant clock.


5. checking no_input_delay
--------------------------
 There are 22 input ports with no input delay specified. (HIGH)

 There are 0 input ports with no input delay but user has a false path constraint.


6. checking no_output_delay
---------------------------
 There are 38 ports with no output delay specified. (HIGH)

 There are 0 ports with no output delay but user has a false path constraint

 There are 0 ports with no output delay but with a timing clock defined on it or propagating through it


7. checking multiple_clock
--------------------------
 There are 0 register/latch pins with multiple clocks.


8. checking generated_clocks
----------------------------
 There are 0 generated clocks that are not connected to a clock source.


9. checking loops
-----------------
 There are 0 combinational loops in the design.


10. checking partial_input_delay
--------------------------------
 There are 0 input ports with partial input delay specified.


11. checking partial_output_delay
---------------------------------
 There are 0 ports with partial output delay specified.


12. checking latch_loops
------------------------
 There are 0 combinational latch loops in the design through latch input



------------------------------------------------------------------------------------------------
| Design Timing Summary
| ---------------------
------------------------------------------------------------------------------------------------

    WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
    -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
     10.238        0.000                      0                82977        0.168        0.000                      0                82977        3.000        0.000                       0                  8605  


All user specified timing constraints are met.


------------------------------------------------------------------------------------------------
| Clock Summary
| -------------
------------------------------------------------------------------------------------------------

Clock              Waveform(ns)       Period(ns)      Frequency(MHz)
-----              ------------       ----------      --------------
fpga_clk           {0.000 5.000}      10.000          100.000         
  clk_out1_cpuclk  {0.000 20.000}     40.000          25.000          
  clkfbout_cpuclk  {0.000 20.000}     40.000          25.000          


------------------------------------------------------------------------------------------------
| Intra Clock Table
| -----------------
------------------------------------------------------------------------------------------------

Clock                  WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
-----                  -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
fpga_clk                                                                                                                                                             3.000        0.000                       0                     1  
  clk_out1_cpuclk       10.238        0.000                      0                82977        0.168        0.000                      0                82977       18.750        0.000                       0                  8601  
  clkfbout_cpuclk                                                                                                                                                   12.633        0.000                       0                     3  


------------------------------------------------------------------------------------------------
| Inter Clock Table
| -----------------
------------------------------------------------------------------------------------------------

From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  


------------------------------------------------------------------------------------------------
| Other Path Groups Table
| -----------------------
------------------------------------------------------------------------------------------------

Path Group    From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    ----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  


------------------------------------------------------------------------------------------------
| Timing Details
| --------------
------------------------------------------------------------------------------------------------


---------------------------------------------------------------------------------------------------
From Clock:  fpga_clk
  To Clock:  fpga_clk

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack        3.000ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         fpga_clk
Waveform(ns):       { 0.000 5.000 }
Period(ns):         10.000
Sources:            { fpga_clk }

Check Type        Corner  Lib Pin           Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     PLLE2_ADV/CLKIN1  n/a            1.249         10.000      8.751      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Max Period        n/a     PLLE2_ADV/CLKIN1  n/a            52.633        10.000      42.633     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Low Pulse Width   Slow    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Low Pulse Width   Fast    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
High Pulse Width  Slow    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
High Pulse Width  Fast    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1



---------------------------------------------------------------------------------------------------
From Clock:  clk_out1_cpuclk
  To Clock:  clk_out1_cpuclk

Setup :            0  Failing Endpoints,  Worst Slack       10.238ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.168ns,  Total Violation        0.000ns
PW    :            0  Failing Endpoints,  Worst Slack       18.750ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             10.238ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r1_0_31_24_29/RAMC/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        29.277ns  (logic 4.010ns (13.696%)  route 25.267ns (86.304%))
  Logic Levels:           20  (LUT2=1 LUT3=2 LUT5=2 LUT6=9 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.131ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.604ns = ( 40.604 - 40.000 ) 
    Source Clock Delay      (SCD):    0.563ns
    Clock Pessimism Removal (CPR):    -0.171ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.219    -1.779    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.655 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.557    -1.098    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.002 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.565     0.563    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X39Y44         FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y44         FDCE (Prop_fdce_C_Q)         0.456     1.019 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=55, routed)          1.989     3.009    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X15Y37         LUT6 (Prop_lut6_I0_O)        0.124     3.133 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.566    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X15Y37         LUT6 (Prop_lut6_I1_O)        0.124     3.690 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.549     4.239    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X28Y37         LUT5 (Prop_lut5_I2_O)        0.124     4.363 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.803     6.166    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRB0
    SLICE_X42Y44         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.124     6.290 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMB_D1/O
                         net (fo=2, routed)           1.108     7.399    Core_cpu/U_RF/rD20[3]
    SLICE_X37Y42         LUT6 (Prop_lut6_I0_O)        0.124     7.523 r  Core_cpu/U_RF/Mem_DRAM_i_43/O
                         net (fo=261, routed)         0.909     8.432    Core_cpu/U_RF/Bus_wdata[3]
    SLICE_X44Y46         LUT6 (Prop_lut6_I0_O)        0.124     8.556 r  Core_cpu/U_RF/C2_carry_i_14/O
                         net (fo=91, routed)          1.322     9.878    Core_cpu/U_RF/alu_B[3]
    SLICE_X48Y52         LUT6 (Prop_lut6_I2_O)        0.124    10.002 r  Core_cpu/U_RF/Mem_DRAM_i_159/O
                         net (fo=4, routed)           0.857    10.859    Core_cpu/U_RF/Mem_DRAM_i_159_n_0
    SLICE_X48Y56         LUT3 (Prop_lut3_I1_O)        0.124    10.983 f  Core_cpu/U_RF/Mem_DRAM_i_244/O
                         net (fo=5, routed)           1.159    12.141    Core_cpu/U_RF/Mem_DRAM_i_244_n_0
    SLICE_X62Y51         LUT5 (Prop_lut5_I1_O)        0.124    12.265 r  Core_cpu/U_RF/Mem_DRAM_i_176/O
                         net (fo=1, routed)           0.996    13.262    Core_cpu/U_RF/Mem_DRAM_i_176_n_0
    SLICE_X63Y51         LUT6 (Prop_lut6_I1_O)        0.124    13.386 f  Core_cpu/U_RF/Mem_DRAM_i_101/O
                         net (fo=7, routed)           1.353    14.739    Core_cpu/U_RF/Mem_DRAM_i_101_n_0
    SLICE_X41Y43         LUT2 (Prop_lut2_I1_O)        0.124    14.863 r  Core_cpu/U_RF/Mem_DRAM_i_13/O
                         net (fo=8192, routed)        5.226    20.089    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28/A1
    SLICE_X10Y122        RAMS64E (Prop_rams64e_ADR1_O)
                                                      0.430    20.519 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28/RAMS64E_A/O
                         net (fo=1, routed)           0.000    20.519    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28/OA
    SLICE_X10Y122        MUXF7 (Prop_muxf7_I1_O)      0.214    20.733 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28/F7.A/O
                         net (fo=1, routed)           0.000    20.733    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28/O1
    SLICE_X10Y122        MUXF8 (Prop_muxf8_I1_O)      0.088    20.821 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28/F8/O
                         net (fo=1, routed)           1.322    22.143    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28_n_0
    SLICE_X32Y126        LUT6 (Prop_lut6_I1_O)        0.319    22.462 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0_i_17/O
                         net (fo=1, routed)           0.000    22.462    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0_i_17_n_0
    SLICE_X32Y126        MUXF7 (Prop_muxf7_I0_O)      0.238    22.700 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0_i_7/O
                         net (fo=1, routed)           0.000    22.700    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0_i_7_n_0
    SLICE_X32Y126        MUXF8 (Prop_muxf8_I0_O)      0.104    22.804 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0_i_2/O
                         net (fo=1, routed)           1.451    24.255    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0_i_2_n_0
    SLICE_X32Y108        LUT6 (Prop_lut6_I1_O)        0.316    24.571 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0/O
                         net (fo=1, routed)           2.984    27.555    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1[28]
    SLICE_X39Y43         LUT3 (Prop_lut3_I2_O)        0.154    27.709 r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_23/O
                         net (fo=1, routed)           1.194    28.903    Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_23_n_0
    SLICE_X35Y55         LUT6 (Prop_lut6_I4_O)        0.327    29.230 r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_6/O
                         net (fo=2, routed)           0.611    29.840    Core_cpu/U_RF/registers_reg_r1_0_31_24_29/DIC0
    SLICE_X38Y56         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29/RAMC/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.969    38.478    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.578 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.501    39.078    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.169 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.435    40.604    Core_cpu/U_RF/registers_reg_r1_0_31_24_29/WCLK
    SLICE_X38Y56         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29/RAMC/CLK
                         clock pessimism             -0.171    40.433    
                         clock uncertainty           -0.180    40.253    
    SLICE_X38Y56         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.175    40.078    Core_cpu/U_RF/registers_reg_r1_0_31_24_29/RAMC
  -------------------------------------------------------------------
                         required time                         40.078    
                         arrival time                         -29.840    
  -------------------------------------------------------------------
                         slack                                 10.238    

Slack (MET) :             10.335ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r2_0_31_24_29/RAMC/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        29.179ns  (logic 4.010ns (13.742%)  route 25.169ns (86.258%))
  Logic Levels:           20  (LUT2=1 LUT3=2 LUT5=2 LUT6=9 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.132ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.603ns = ( 40.603 - 40.000 ) 
    Source Clock Delay      (SCD):    0.563ns
    Clock Pessimism Removal (CPR):    -0.171ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.219    -1.779    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.655 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.557    -1.098    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.002 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.565     0.563    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X39Y44         FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y44         FDCE (Prop_fdce_C_Q)         0.456     1.019 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=55, routed)          1.989     3.009    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X15Y37         LUT6 (Prop_lut6_I0_O)        0.124     3.133 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.566    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X15Y37         LUT6 (Prop_lut6_I1_O)        0.124     3.690 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.549     4.239    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X28Y37         LUT5 (Prop_lut5_I2_O)        0.124     4.363 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.803     6.166    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRB0
    SLICE_X42Y44         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.124     6.290 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMB_D1/O
                         net (fo=2, routed)           1.108     7.399    Core_cpu/U_RF/rD20[3]
    SLICE_X37Y42         LUT6 (Prop_lut6_I0_O)        0.124     7.523 r  Core_cpu/U_RF/Mem_DRAM_i_43/O
                         net (fo=261, routed)         0.909     8.432    Core_cpu/U_RF/Bus_wdata[3]
    SLICE_X44Y46         LUT6 (Prop_lut6_I0_O)        0.124     8.556 r  Core_cpu/U_RF/C2_carry_i_14/O
                         net (fo=91, routed)          1.322     9.878    Core_cpu/U_RF/alu_B[3]
    SLICE_X48Y52         LUT6 (Prop_lut6_I2_O)        0.124    10.002 r  Core_cpu/U_RF/Mem_DRAM_i_159/O
                         net (fo=4, routed)           0.857    10.859    Core_cpu/U_RF/Mem_DRAM_i_159_n_0
    SLICE_X48Y56         LUT3 (Prop_lut3_I1_O)        0.124    10.983 f  Core_cpu/U_RF/Mem_DRAM_i_244/O
                         net (fo=5, routed)           1.159    12.141    Core_cpu/U_RF/Mem_DRAM_i_244_n_0
    SLICE_X62Y51         LUT5 (Prop_lut5_I1_O)        0.124    12.265 r  Core_cpu/U_RF/Mem_DRAM_i_176/O
                         net (fo=1, routed)           0.996    13.262    Core_cpu/U_RF/Mem_DRAM_i_176_n_0
    SLICE_X63Y51         LUT6 (Prop_lut6_I1_O)        0.124    13.386 f  Core_cpu/U_RF/Mem_DRAM_i_101/O
                         net (fo=7, routed)           1.353    14.739    Core_cpu/U_RF/Mem_DRAM_i_101_n_0
    SLICE_X41Y43         LUT2 (Prop_lut2_I1_O)        0.124    14.863 r  Core_cpu/U_RF/Mem_DRAM_i_13/O
                         net (fo=8192, routed)        5.226    20.089    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28/A1
    SLICE_X10Y122        RAMS64E (Prop_rams64e_ADR1_O)
                                                      0.430    20.519 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28/RAMS64E_A/O
                         net (fo=1, routed)           0.000    20.519    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28/OA
    SLICE_X10Y122        MUXF7 (Prop_muxf7_I1_O)      0.214    20.733 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28/F7.A/O
                         net (fo=1, routed)           0.000    20.733    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28/O1
    SLICE_X10Y122        MUXF8 (Prop_muxf8_I1_O)      0.088    20.821 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28/F8/O
                         net (fo=1, routed)           1.322    22.143    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_8704_8959_28_28_n_0
    SLICE_X32Y126        LUT6 (Prop_lut6_I1_O)        0.319    22.462 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0_i_17/O
                         net (fo=1, routed)           0.000    22.462    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0_i_17_n_0
    SLICE_X32Y126        MUXF7 (Prop_muxf7_I0_O)      0.238    22.700 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0_i_7/O
                         net (fo=1, routed)           0.000    22.700    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0_i_7_n_0
    SLICE_X32Y126        MUXF8 (Prop_muxf8_I0_O)      0.104    22.804 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0_i_2/O
                         net (fo=1, routed)           1.451    24.255    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0_i_2_n_0
    SLICE_X32Y108        LUT6 (Prop_lut6_I1_O)        0.316    24.571 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[28]_INST_0/O
                         net (fo=1, routed)           2.984    27.555    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1[28]
    SLICE_X39Y43         LUT3 (Prop_lut3_I2_O)        0.154    27.709 r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_23/O
                         net (fo=1, routed)           1.194    28.903    Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_23_n_0
    SLICE_X35Y55         LUT6 (Prop_lut6_I4_O)        0.327    29.230 r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_6/O
                         net (fo=2, routed)           0.513    29.742    Core_cpu/U_RF/registers_reg_r2_0_31_24_29/DIC0
    SLICE_X34Y56         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_24_29/RAMC/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.969    38.478    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.578 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.501    39.078    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.169 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.434    40.603    Core_cpu/U_RF/registers_reg_r2_0_31_24_29/WCLK
    SLICE_X34Y56         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_24_29/RAMC/CLK
                         clock pessimism             -0.171    40.432    
                         clock uncertainty           -0.180    40.252    
    SLICE_X34Y56         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.175    40.077    Core_cpu/U_RF/registers_reg_r2_0_31_24_29/RAMC
  -------------------------------------------------------------------
                         required time                         40.077    
                         arrival time                         -29.742    
  -------------------------------------------------------------------
                         slack                                 10.335    

Slack (MET) :             11.167ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r1_0_31_30_31/RAMA/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        28.363ns  (logic 3.844ns (13.553%)  route 24.519ns (86.447%))
  Logic Levels:           20  (LUT2=1 LUT3=2 LUT5=2 LUT6=9 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.130ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.605ns = ( 40.605 - 40.000 ) 
    Source Clock Delay      (SCD):    0.563ns
    Clock Pessimism Removal (CPR):    -0.171ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.219    -1.779    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.655 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.557    -1.098    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.002 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.565     0.563    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X39Y44         FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y44         FDCE (Prop_fdce_C_Q)         0.456     1.019 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=55, routed)          1.989     3.009    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X15Y37         LUT6 (Prop_lut6_I0_O)        0.124     3.133 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.566    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X15Y37         LUT6 (Prop_lut6_I1_O)        0.124     3.690 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.549     4.239    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X28Y37         LUT5 (Prop_lut5_I2_O)        0.124     4.363 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.803     6.166    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRB0
    SLICE_X42Y44         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.124     6.290 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMB_D1/O
                         net (fo=2, routed)           1.108     7.399    Core_cpu/U_RF/rD20[3]
    SLICE_X37Y42         LUT6 (Prop_lut6_I0_O)        0.124     7.523 r  Core_cpu/U_RF/Mem_DRAM_i_43/O
                         net (fo=261, routed)         0.909     8.432    Core_cpu/U_RF/Bus_wdata[3]
    SLICE_X44Y46         LUT6 (Prop_lut6_I0_O)        0.124     8.556 r  Core_cpu/U_RF/C2_carry_i_14/O
                         net (fo=91, routed)          1.322     9.878    Core_cpu/U_RF/alu_B[3]
    SLICE_X48Y52         LUT6 (Prop_lut6_I2_O)        0.124    10.002 r  Core_cpu/U_RF/Mem_DRAM_i_159/O
                         net (fo=4, routed)           0.857    10.859    Core_cpu/U_RF/Mem_DRAM_i_159_n_0
    SLICE_X48Y56         LUT3 (Prop_lut3_I1_O)        0.124    10.983 f  Core_cpu/U_RF/Mem_DRAM_i_244/O
                         net (fo=5, routed)           1.159    12.141    Core_cpu/U_RF/Mem_DRAM_i_244_n_0
    SLICE_X62Y51         LUT5 (Prop_lut5_I1_O)        0.124    12.265 r  Core_cpu/U_RF/Mem_DRAM_i_176/O
                         net (fo=1, routed)           0.996    13.262    Core_cpu/U_RF/Mem_DRAM_i_176_n_0
    SLICE_X63Y51         LUT6 (Prop_lut6_I1_O)        0.124    13.386 f  Core_cpu/U_RF/Mem_DRAM_i_101/O
                         net (fo=7, routed)           1.353    14.739    Core_cpu/U_RF/Mem_DRAM_i_101_n_0
    SLICE_X41Y43         LUT2 (Prop_lut2_I1_O)        0.124    14.863 r  Core_cpu/U_RF/Mem_DRAM_i_13/O
                         net (fo=8192, routed)        5.754    20.616    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30/A1
    SLICE_X12Y126        RAMS64E (Prop_rams64e_ADR1_O)
                                                      0.262    20.878 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30/RAMS64E_A/O
                         net (fo=1, routed)           0.000    20.878    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30/OA
    SLICE_X12Y126        MUXF7 (Prop_muxf7_I1_O)      0.214    21.092 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30/F7.A/O
                         net (fo=1, routed)           0.000    21.092    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30/O1
    SLICE_X12Y126        MUXF8 (Prop_muxf8_I1_O)      0.088    21.180 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30/F8/O
                         net (fo=1, routed)           1.161    22.342    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30_n_0
    SLICE_X13Y117        LUT6 (Prop_lut6_I1_O)        0.319    22.661 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0_i_26/O
                         net (fo=1, routed)           0.000    22.661    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0_i_26_n_0
    SLICE_X13Y117        MUXF7 (Prop_muxf7_I1_O)      0.245    22.906 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0_i_11/O
                         net (fo=1, routed)           0.000    22.906    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0_i_11_n_0
    SLICE_X13Y117        MUXF8 (Prop_muxf8_I0_O)      0.104    23.010 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0_i_4/O
                         net (fo=1, routed)           0.783    23.793    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0_i_4_n_0
    SLICE_X13Y108        LUT6 (Prop_lut6_I5_O)        0.316    24.109 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0/O
                         net (fo=1, routed)           2.535    26.645    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1[30]
    SLICE_X15Y56         LUT3 (Prop_lut3_I2_O)        0.150    26.795 r  Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_8/O
                         net (fo=1, routed)           0.991    27.786    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_8_n_0
    SLICE_X31Y56         LUT6 (Prop_lut6_I5_O)        0.326    28.112 r  Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_2/O
                         net (fo=2, routed)           0.814    28.926    Core_cpu/U_RF/registers_reg_r1_0_31_30_31/DIA0
    SLICE_X42Y58         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_30_31/RAMA/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.969    38.478    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.578 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.501    39.078    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.169 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.436    40.605    Core_cpu/U_RF/registers_reg_r1_0_31_30_31/WCLK
    SLICE_X42Y58         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_30_31/RAMA/CLK
                         clock pessimism             -0.171    40.434    
                         clock uncertainty           -0.180    40.254    
    SLICE_X42Y58         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.161    40.093    Core_cpu/U_RF/registers_reg_r1_0_31_30_31/RAMA
  -------------------------------------------------------------------
                         required time                         40.093    
                         arrival time                         -28.926    
  -------------------------------------------------------------------
                         slack                                 11.167    

Slack (MET) :             11.214ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r2_0_31_30_31/RAMA_D1/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        28.216ns  (logic 4.380ns (15.525%)  route 23.836ns (84.475%))
  Logic Levels:           20  (LUT2=1 LUT3=2 LUT5=1 LUT6=10 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.132ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.603ns = ( 40.603 - 40.000 ) 
    Source Clock Delay      (SCD):    0.563ns
    Clock Pessimism Removal (CPR):    -0.171ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.219    -1.779    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.655 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.557    -1.098    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.002 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.565     0.563    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X39Y44         FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y44         FDCE (Prop_fdce_C_Q)         0.456     1.019 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=55, routed)          1.989     3.009    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X15Y37         LUT6 (Prop_lut6_I0_O)        0.124     3.133 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.566    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X15Y37         LUT6 (Prop_lut6_I1_O)        0.124     3.690 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.549     4.239    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X28Y37         LUT5 (Prop_lut5_I2_O)        0.124     4.363 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.803     6.166    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRB0
    SLICE_X42Y44         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.124     6.290 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMB_D1/O
                         net (fo=2, routed)           1.108     7.399    Core_cpu/U_RF/rD20[3]
    SLICE_X37Y42         LUT6 (Prop_lut6_I0_O)        0.124     7.523 r  Core_cpu/U_RF/Mem_DRAM_i_43/O
                         net (fo=261, routed)         0.909     8.432    Core_cpu/U_RF/Bus_wdata[3]
    SLICE_X44Y46         LUT6 (Prop_lut6_I0_O)        0.124     8.556 r  Core_cpu/U_RF/C2_carry_i_14/O
                         net (fo=91, routed)          1.322     9.878    Core_cpu/U_RF/alu_B[3]
    SLICE_X48Y52         LUT6 (Prop_lut6_I2_O)        0.124    10.002 r  Core_cpu/U_RF/Mem_DRAM_i_159/O
                         net (fo=4, routed)           1.431    11.433    Core_cpu/U_RF/Mem_DRAM_i_159_n_0
    SLICE_X62Y53         LUT3 (Prop_lut3_I2_O)        0.152    11.585 r  Core_cpu/U_RF/Mem_DRAM_i_236/O
                         net (fo=4, routed)           0.522    12.107    Core_cpu/U_RF/Mem_DRAM_i_236_n_0
    SLICE_X62Y51         LUT6 (Prop_lut6_I3_O)        0.326    12.433 r  Core_cpu/U_RF/Mem_DRAM_i_170/O
                         net (fo=1, routed)           0.805    13.238    Core_cpu/U_RF/Mem_DRAM_i_170_n_0
    SLICE_X61Y51         LUT6 (Prop_lut6_I1_O)        0.124    13.362 r  Core_cpu/U_RF/Mem_DRAM_i_100/O
                         net (fo=6, routed)           1.913    15.275    Core_cpu/U_RF/Mem_DRAM_i_100_n_0
    SLICE_X35Y60         LUT2 (Prop_lut2_I0_O)        0.152    15.427 r  Core_cpu/U_RF/Mem_DRAM_i_12/O
                         net (fo=8196, routed)        3.554    18.981    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31/A2
    SLICE_X46Y134        RAMS64E (Prop_rams64e_ADR2_O)
                                                      0.768    19.750 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31/RAMS64E_A/O
                         net (fo=1, routed)           0.000    19.750    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31/OA
    SLICE_X46Y134        MUXF7 (Prop_muxf7_I1_O)      0.214    19.964 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31/F7.A/O
                         net (fo=1, routed)           0.000    19.964    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31/O1
    SLICE_X46Y134        MUXF8 (Prop_muxf8_I1_O)      0.088    20.052 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31/F8/O
                         net (fo=1, routed)           1.506    21.557    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31_n_0
    SLICE_X51Y121        LUT6 (Prop_lut6_I1_O)        0.319    21.876 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_14/O
                         net (fo=1, routed)           0.000    21.876    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_14_n_0
    SLICE_X51Y121        MUXF7 (Prop_muxf7_I1_O)      0.245    22.121 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_5/O
                         net (fo=1, routed)           0.000    22.121    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_5_n_0
    SLICE_X51Y121        MUXF8 (Prop_muxf8_I0_O)      0.104    22.225 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_1/O
                         net (fo=1, routed)           1.906    24.132    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_1_n_0
    SLICE_X51Y94         LUT6 (Prop_lut6_I0_O)        0.316    24.448 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0/O
                         net (fo=1, routed)           2.310    26.758    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1[31]
    SLICE_X41Y49         LUT3 (Prop_lut3_I1_O)        0.124    26.882 r  Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_5/O
                         net (fo=1, routed)           1.018    27.899    U_Timer/registers_reg_r2_0_31_30_31_3
    SLICE_X36Y53         LUT6 (Prop_lut6_I5_O)        0.124    28.023 r  U_Timer/registers_reg_r1_0_31_30_31_i_1/O
                         net (fo=2, routed)           0.757    28.780    Core_cpu/U_RF/registers_reg_r2_0_31_30_31/DIA1
    SLICE_X38Y58         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_30_31/RAMA_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.969    38.478    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.578 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.501    39.078    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.169 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.434    40.603    Core_cpu/U_RF/registers_reg_r2_0_31_30_31/WCLK
    SLICE_X38Y58         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_30_31/RAMA_D1/CLK
                         clock pessimism             -0.171    40.432    
                         clock uncertainty           -0.180    40.252    
    SLICE_X38Y58         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.258    39.994    Core_cpu/U_RF/registers_reg_r2_0_31_30_31/RAMA_D1
  -------------------------------------------------------------------
                         required time                         39.994    
                         arrival time                         -28.780    
  -------------------------------------------------------------------
                         slack                                 11.214    

Slack (MET) :             11.299ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r1_0_31_30_31/RAMA_D1/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        28.134ns  (logic 4.380ns (15.570%)  route 23.753ns (84.430%))
  Logic Levels:           20  (LUT2=1 LUT3=2 LUT5=1 LUT6=10 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.130ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.605ns = ( 40.605 - 40.000 ) 
    Source Clock Delay      (SCD):    0.563ns
    Clock Pessimism Removal (CPR):    -0.171ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.219    -1.779    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.655 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.557    -1.098    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.002 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.565     0.563    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X39Y44         FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y44         FDCE (Prop_fdce_C_Q)         0.456     1.019 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=55, routed)          1.989     3.009    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X15Y37         LUT6 (Prop_lut6_I0_O)        0.124     3.133 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.566    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X15Y37         LUT6 (Prop_lut6_I1_O)        0.124     3.690 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.549     4.239    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X28Y37         LUT5 (Prop_lut5_I2_O)        0.124     4.363 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.803     6.166    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRB0
    SLICE_X42Y44         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.124     6.290 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMB_D1/O
                         net (fo=2, routed)           1.108     7.399    Core_cpu/U_RF/rD20[3]
    SLICE_X37Y42         LUT6 (Prop_lut6_I0_O)        0.124     7.523 r  Core_cpu/U_RF/Mem_DRAM_i_43/O
                         net (fo=261, routed)         0.909     8.432    Core_cpu/U_RF/Bus_wdata[3]
    SLICE_X44Y46         LUT6 (Prop_lut6_I0_O)        0.124     8.556 r  Core_cpu/U_RF/C2_carry_i_14/O
                         net (fo=91, routed)          1.322     9.878    Core_cpu/U_RF/alu_B[3]
    SLICE_X48Y52         LUT6 (Prop_lut6_I2_O)        0.124    10.002 r  Core_cpu/U_RF/Mem_DRAM_i_159/O
                         net (fo=4, routed)           1.431    11.433    Core_cpu/U_RF/Mem_DRAM_i_159_n_0
    SLICE_X62Y53         LUT3 (Prop_lut3_I2_O)        0.152    11.585 r  Core_cpu/U_RF/Mem_DRAM_i_236/O
                         net (fo=4, routed)           0.522    12.107    Core_cpu/U_RF/Mem_DRAM_i_236_n_0
    SLICE_X62Y51         LUT6 (Prop_lut6_I3_O)        0.326    12.433 r  Core_cpu/U_RF/Mem_DRAM_i_170/O
                         net (fo=1, routed)           0.805    13.238    Core_cpu/U_RF/Mem_DRAM_i_170_n_0
    SLICE_X61Y51         LUT6 (Prop_lut6_I1_O)        0.124    13.362 r  Core_cpu/U_RF/Mem_DRAM_i_100/O
                         net (fo=6, routed)           1.913    15.275    Core_cpu/U_RF/Mem_DRAM_i_100_n_0
    SLICE_X35Y60         LUT2 (Prop_lut2_I0_O)        0.152    15.427 r  Core_cpu/U_RF/Mem_DRAM_i_12/O
                         net (fo=8196, routed)        3.554    18.981    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31/A2
    SLICE_X46Y134        RAMS64E (Prop_rams64e_ADR2_O)
                                                      0.768    19.750 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31/RAMS64E_A/O
                         net (fo=1, routed)           0.000    19.750    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31/OA
    SLICE_X46Y134        MUXF7 (Prop_muxf7_I1_O)      0.214    19.964 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31/F7.A/O
                         net (fo=1, routed)           0.000    19.964    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31/O1
    SLICE_X46Y134        MUXF8 (Prop_muxf8_I1_O)      0.088    20.052 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31/F8/O
                         net (fo=1, routed)           1.506    21.557    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_31_31_n_0
    SLICE_X51Y121        LUT6 (Prop_lut6_I1_O)        0.319    21.876 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_14/O
                         net (fo=1, routed)           0.000    21.876    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_14_n_0
    SLICE_X51Y121        MUXF7 (Prop_muxf7_I1_O)      0.245    22.121 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_5/O
                         net (fo=1, routed)           0.000    22.121    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_5_n_0
    SLICE_X51Y121        MUXF8 (Prop_muxf8_I0_O)      0.104    22.225 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_1/O
                         net (fo=1, routed)           1.906    24.132    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0_i_1_n_0
    SLICE_X51Y94         LUT6 (Prop_lut6_I0_O)        0.316    24.448 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[31]_INST_0/O
                         net (fo=1, routed)           2.310    26.758    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1[31]
    SLICE_X41Y49         LUT3 (Prop_lut3_I1_O)        0.124    26.882 r  Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_5/O
                         net (fo=1, routed)           1.018    27.899    U_Timer/registers_reg_r2_0_31_30_31_3
    SLICE_X36Y53         LUT6 (Prop_lut6_I5_O)        0.124    28.023 r  U_Timer/registers_reg_r1_0_31_30_31_i_1/O
                         net (fo=2, routed)           0.674    28.697    Core_cpu/U_RF/registers_reg_r1_0_31_30_31/DIA1
    SLICE_X42Y58         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_30_31/RAMA_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.969    38.478    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.578 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.501    39.078    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.169 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.436    40.605    Core_cpu/U_RF/registers_reg_r1_0_31_30_31/WCLK
    SLICE_X42Y58         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_30_31/RAMA_D1/CLK
                         clock pessimism             -0.171    40.434    
                         clock uncertainty           -0.180    40.254    
    SLICE_X42Y58         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.258    39.996    Core_cpu/U_RF/registers_reg_r1_0_31_30_31/RAMA_D1
  -------------------------------------------------------------------
                         required time                         39.996    
                         arrival time                         -28.697    
  -------------------------------------------------------------------
                         slack                                 11.299    

Slack (MET) :             11.315ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r2_0_31_30_31/RAMA/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        28.213ns  (logic 3.844ns (13.625%)  route 24.369ns (86.375%))
  Logic Levels:           20  (LUT2=1 LUT3=2 LUT5=2 LUT6=9 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.132ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.603ns = ( 40.603 - 40.000 ) 
    Source Clock Delay      (SCD):    0.563ns
    Clock Pessimism Removal (CPR):    -0.171ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.219    -1.779    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.655 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.557    -1.098    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.002 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.565     0.563    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X39Y44         FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y44         FDCE (Prop_fdce_C_Q)         0.456     1.019 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=55, routed)          1.989     3.009    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X15Y37         LUT6 (Prop_lut6_I0_O)        0.124     3.133 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.566    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X15Y37         LUT6 (Prop_lut6_I1_O)        0.124     3.690 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.549     4.239    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X28Y37         LUT5 (Prop_lut5_I2_O)        0.124     4.363 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.803     6.166    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRB0
    SLICE_X42Y44         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.124     6.290 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMB_D1/O
                         net (fo=2, routed)           1.108     7.399    Core_cpu/U_RF/rD20[3]
    SLICE_X37Y42         LUT6 (Prop_lut6_I0_O)        0.124     7.523 r  Core_cpu/U_RF/Mem_DRAM_i_43/O
                         net (fo=261, routed)         0.909     8.432    Core_cpu/U_RF/Bus_wdata[3]
    SLICE_X44Y46         LUT6 (Prop_lut6_I0_O)        0.124     8.556 r  Core_cpu/U_RF/C2_carry_i_14/O
                         net (fo=91, routed)          1.322     9.878    Core_cpu/U_RF/alu_B[3]
    SLICE_X48Y52         LUT6 (Prop_lut6_I2_O)        0.124    10.002 r  Core_cpu/U_RF/Mem_DRAM_i_159/O
                         net (fo=4, routed)           0.857    10.859    Core_cpu/U_RF/Mem_DRAM_i_159_n_0
    SLICE_X48Y56         LUT3 (Prop_lut3_I1_O)        0.124    10.983 f  Core_cpu/U_RF/Mem_DRAM_i_244/O
                         net (fo=5, routed)           1.159    12.141    Core_cpu/U_RF/Mem_DRAM_i_244_n_0
    SLICE_X62Y51         LUT5 (Prop_lut5_I1_O)        0.124    12.265 r  Core_cpu/U_RF/Mem_DRAM_i_176/O
                         net (fo=1, routed)           0.996    13.262    Core_cpu/U_RF/Mem_DRAM_i_176_n_0
    SLICE_X63Y51         LUT6 (Prop_lut6_I1_O)        0.124    13.386 f  Core_cpu/U_RF/Mem_DRAM_i_101/O
                         net (fo=7, routed)           1.353    14.739    Core_cpu/U_RF/Mem_DRAM_i_101_n_0
    SLICE_X41Y43         LUT2 (Prop_lut2_I1_O)        0.124    14.863 r  Core_cpu/U_RF/Mem_DRAM_i_13/O
                         net (fo=8192, routed)        5.754    20.616    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30/A1
    SLICE_X12Y126        RAMS64E (Prop_rams64e_ADR1_O)
                                                      0.262    20.878 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30/RAMS64E_A/O
                         net (fo=1, routed)           0.000    20.878    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30/OA
    SLICE_X12Y126        MUXF7 (Prop_muxf7_I1_O)      0.214    21.092 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30/F7.A/O
                         net (fo=1, routed)           0.000    21.092    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30/O1
    SLICE_X12Y126        MUXF8 (Prop_muxf8_I1_O)      0.088    21.180 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30/F8/O
                         net (fo=1, routed)           1.161    22.342    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_1536_1791_30_30_n_0
    SLICE_X13Y117        LUT6 (Prop_lut6_I1_O)        0.319    22.661 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0_i_26/O
                         net (fo=1, routed)           0.000    22.661    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0_i_26_n_0
    SLICE_X13Y117        MUXF7 (Prop_muxf7_I1_O)      0.245    22.906 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0_i_11/O
                         net (fo=1, routed)           0.000    22.906    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0_i_11_n_0
    SLICE_X13Y117        MUXF8 (Prop_muxf8_I0_O)      0.104    23.010 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0_i_4/O
                         net (fo=1, routed)           0.783    23.793    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0_i_4_n_0
    SLICE_X13Y108        LUT6 (Prop_lut6_I5_O)        0.316    24.109 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[30]_INST_0/O
                         net (fo=1, routed)           2.535    26.645    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1[30]
    SLICE_X15Y56         LUT3 (Prop_lut3_I2_O)        0.150    26.795 r  Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_8/O
                         net (fo=1, routed)           0.991    27.786    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_8_n_0
    SLICE_X31Y56         LUT6 (Prop_lut6_I5_O)        0.326    28.112 r  Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_2/O
                         net (fo=2, routed)           0.664    28.776    Core_cpu/U_RF/registers_reg_r2_0_31_30_31/DIA0
    SLICE_X38Y58         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_30_31/RAMA/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.969    38.478    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.578 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.501    39.078    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.169 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.434    40.603    Core_cpu/U_RF/registers_reg_r2_0_31_30_31/WCLK
    SLICE_X38Y58         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_30_31/RAMA/CLK
                         clock pessimism             -0.171    40.432    
                         clock uncertainty           -0.180    40.252    
    SLICE_X38Y58         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.161    40.091    Core_cpu/U_RF/registers_reg_r2_0_31_30_31/RAMA
  -------------------------------------------------------------------
                         required time                         40.091    
                         arrival time                         -28.776    
  -------------------------------------------------------------------
                         slack                                 11.315    

Slack (MET) :             11.699ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r1_0_31_24_29/RAMA_D1/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.733ns  (logic 3.588ns (12.936%)  route 24.145ns (87.064%))
  Logic Levels:           20  (LUT2=1 LUT3=2 LUT5=2 LUT6=9 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.131ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.604ns = ( 40.604 - 40.000 ) 
    Source Clock Delay      (SCD):    0.563ns
    Clock Pessimism Removal (CPR):    -0.171ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.219    -1.779    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.655 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.557    -1.098    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.002 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.565     0.563    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X39Y44         FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y44         FDCE (Prop_fdce_C_Q)         0.456     1.019 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=55, routed)          1.989     3.009    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X15Y37         LUT6 (Prop_lut6_I0_O)        0.124     3.133 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.566    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X15Y37         LUT6 (Prop_lut6_I1_O)        0.124     3.690 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.549     4.239    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X28Y37         LUT5 (Prop_lut5_I2_O)        0.124     4.363 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.803     6.166    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRB0
    SLICE_X42Y44         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.124     6.290 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMB_D1/O
                         net (fo=2, routed)           1.108     7.399    Core_cpu/U_RF/rD20[3]
    SLICE_X37Y42         LUT6 (Prop_lut6_I0_O)        0.124     7.523 r  Core_cpu/U_RF/Mem_DRAM_i_43/O
                         net (fo=261, routed)         0.909     8.432    Core_cpu/U_RF/Bus_wdata[3]
    SLICE_X44Y46         LUT6 (Prop_lut6_I0_O)        0.124     8.556 r  Core_cpu/U_RF/C2_carry_i_14/O
                         net (fo=91, routed)          1.322     9.878    Core_cpu/U_RF/alu_B[3]
    SLICE_X48Y52         LUT6 (Prop_lut6_I2_O)        0.124    10.002 r  Core_cpu/U_RF/Mem_DRAM_i_159/O
                         net (fo=4, routed)           0.857    10.859    Core_cpu/U_RF/Mem_DRAM_i_159_n_0
    SLICE_X48Y56         LUT3 (Prop_lut3_I1_O)        0.124    10.983 f  Core_cpu/U_RF/Mem_DRAM_i_244/O
                         net (fo=5, routed)           1.159    12.141    Core_cpu/U_RF/Mem_DRAM_i_244_n_0
    SLICE_X62Y51         LUT5 (Prop_lut5_I1_O)        0.124    12.265 r  Core_cpu/U_RF/Mem_DRAM_i_176/O
                         net (fo=1, routed)           0.996    13.262    Core_cpu/U_RF/Mem_DRAM_i_176_n_0
    SLICE_X63Y51         LUT6 (Prop_lut6_I1_O)        0.124    13.386 f  Core_cpu/U_RF/Mem_DRAM_i_101/O
                         net (fo=7, routed)           1.353    14.739    Core_cpu/U_RF/Mem_DRAM_i_101_n_0
    SLICE_X41Y43         LUT2 (Prop_lut2_I1_O)        0.124    14.863 r  Core_cpu/U_RF/Mem_DRAM_i_13/O
                         net (fo=8192, routed)        5.318    20.181    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25/A1
    SLICE_X14Y127        RAMS64E (Prop_rams64e_ADR1_O)
                                                      0.272    20.453 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25/RAMS64E_A/O
                         net (fo=1, routed)           0.000    20.453    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25/OA
    SLICE_X14Y127        MUXF7 (Prop_muxf7_I1_O)      0.214    20.667 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25/F7.A/O
                         net (fo=1, routed)           0.000    20.667    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25/O1
    SLICE_X14Y127        MUXF8 (Prop_muxf8_I1_O)      0.088    20.755 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25/F8/O
                         net (fo=1, routed)           1.347    22.102    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25_n_0
    SLICE_X28Y116        LUT6 (Prop_lut6_I1_O)        0.319    22.421 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0_i_20/O
                         net (fo=1, routed)           0.000    22.421    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0_i_20_n_0
    SLICE_X28Y116        MUXF7 (Prop_muxf7_I1_O)      0.217    22.638 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0_i_8/O
                         net (fo=1, routed)           0.000    22.638    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0_i_8_n_0
    SLICE_X28Y116        MUXF8 (Prop_muxf8_I1_O)      0.094    22.732 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0_i_2/O
                         net (fo=1, routed)           1.299    24.031    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0_i_2_n_0
    SLICE_X28Y95         LUT6 (Prop_lut6_I1_O)        0.316    24.347 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0/O
                         net (fo=1, routed)           2.246    26.593    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1[25]
    SLICE_X33Y55         LUT3 (Prop_lut3_I2_O)        0.124    26.717 r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_8/O
                         net (fo=1, routed)           0.824    27.542    Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_8_n_0
    SLICE_X33Y56         LUT6 (Prop_lut6_I4_O)        0.124    27.666 r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_1/O
                         net (fo=2, routed)           0.631    28.296    Core_cpu/U_RF/registers_reg_r1_0_31_24_29/DIA1
    SLICE_X38Y56         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29/RAMA_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.969    38.478    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.578 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.501    39.078    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.169 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.435    40.604    Core_cpu/U_RF/registers_reg_r1_0_31_24_29/WCLK
    SLICE_X38Y56         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29/RAMA_D1/CLK
                         clock pessimism             -0.171    40.433    
                         clock uncertainty           -0.180    40.253    
    SLICE_X38Y56         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.258    39.995    Core_cpu/U_RF/registers_reg_r1_0_31_24_29/RAMA_D1
  -------------------------------------------------------------------
                         required time                         39.995    
                         arrival time                         -28.296    
  -------------------------------------------------------------------
                         slack                                 11.699    

Slack (MET) :             11.943ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r1_0_31_6_11/RAMB/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.656ns  (logic 4.217ns (15.248%)  route 23.439ns (84.752%))
  Logic Levels:           20  (LUT2=1 LUT3=2 LUT5=1 LUT6=10 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.036ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.619ns = ( 40.619 - 40.000 ) 
    Source Clock Delay      (SCD):    0.563ns
    Clock Pessimism Removal (CPR):    -0.091ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.219    -1.779    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.655 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.557    -1.098    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.002 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.565     0.563    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X39Y44         FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y44         FDCE (Prop_fdce_C_Q)         0.456     1.019 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=55, routed)          1.989     3.009    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X15Y37         LUT6 (Prop_lut6_I0_O)        0.124     3.133 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.566    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X15Y37         LUT6 (Prop_lut6_I1_O)        0.124     3.690 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.549     4.239    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X28Y37         LUT5 (Prop_lut5_I2_O)        0.124     4.363 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.803     6.166    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRB0
    SLICE_X42Y44         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.124     6.290 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMB_D1/O
                         net (fo=2, routed)           1.108     7.399    Core_cpu/U_RF/rD20[3]
    SLICE_X37Y42         LUT6 (Prop_lut6_I0_O)        0.124     7.523 r  Core_cpu/U_RF/Mem_DRAM_i_43/O
                         net (fo=261, routed)         0.909     8.432    Core_cpu/U_RF/Bus_wdata[3]
    SLICE_X44Y46         LUT6 (Prop_lut6_I0_O)        0.124     8.556 r  Core_cpu/U_RF/C2_carry_i_14/O
                         net (fo=91, routed)          1.322     9.878    Core_cpu/U_RF/alu_B[3]
    SLICE_X48Y52         LUT6 (Prop_lut6_I2_O)        0.124    10.002 r  Core_cpu/U_RF/Mem_DRAM_i_159/O
                         net (fo=4, routed)           1.431    11.433    Core_cpu/U_RF/Mem_DRAM_i_159_n_0
    SLICE_X62Y53         LUT3 (Prop_lut3_I2_O)        0.152    11.585 r  Core_cpu/U_RF/Mem_DRAM_i_236/O
                         net (fo=4, routed)           0.522    12.107    Core_cpu/U_RF/Mem_DRAM_i_236_n_0
    SLICE_X62Y51         LUT6 (Prop_lut6_I3_O)        0.326    12.433 r  Core_cpu/U_RF/Mem_DRAM_i_170/O
                         net (fo=1, routed)           0.805    13.238    Core_cpu/U_RF/Mem_DRAM_i_170_n_0
    SLICE_X61Y51         LUT6 (Prop_lut6_I1_O)        0.124    13.362 r  Core_cpu/U_RF/Mem_DRAM_i_100/O
                         net (fo=6, routed)           1.913    15.275    Core_cpu/U_RF/Mem_DRAM_i_100_n_0
    SLICE_X35Y60         LUT2 (Prop_lut2_I0_O)        0.152    15.427 r  Core_cpu/U_RF/Mem_DRAM_i_12/O
                         net (fo=8196, routed)        3.955    19.382    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8/A2
    SLICE_X38Y4          RAMS64E (Prop_rams64e_ADR2_O)
                                                      0.332    19.714 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8/RAMS64E_D/O
                         net (fo=1, routed)           0.000    19.714    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8/OD
    SLICE_X38Y4          MUXF7 (Prop_muxf7_I0_O)      0.241    19.955 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8/F7.B/O
                         net (fo=1, routed)           0.000    19.955    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8/O0
    SLICE_X38Y4          MUXF8 (Prop_muxf8_I0_O)      0.098    20.053 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8/F8/O
                         net (fo=1, routed)           0.859    20.912    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8_n_0
    SLICE_X35Y3          LUT6 (Prop_lut6_I0_O)        0.319    21.231 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0_i_14/O
                         net (fo=1, routed)           0.000    21.231    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0_i_14_n_0
    SLICE_X35Y3          MUXF7 (Prop_muxf7_I1_O)      0.245    21.476 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0_i_5/O
                         net (fo=1, routed)           0.000    21.476    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0_i_5_n_0
    SLICE_X35Y3          MUXF8 (Prop_muxf8_I0_O)      0.104    21.580 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0_i_1/O
                         net (fo=1, routed)           1.195    22.775    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0_i_1_n_0
    SLICE_X33Y11         LUT6 (Prop_lut6_I0_O)        0.316    23.091 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0/O
                         net (fo=1, routed)           2.350    25.441    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1[8]
    SLICE_X33Y55         LUT3 (Prop_lut3_I2_O)        0.152    25.593 r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_21/O
                         net (fo=1, routed)           1.341    26.934    Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_21_n_0
    SLICE_X31Y45         LUT6 (Prop_lut6_I4_O)        0.332    27.266 r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_4/O
                         net (fo=2, routed)           0.953    28.220    Core_cpu/U_RF/registers_reg_r1_0_31_6_11/DIB0
    SLICE_X46Y47         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11/RAMB/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.969    38.478    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.578 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.501    39.078    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.169 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.449    40.619    Core_cpu/U_RF/registers_reg_r1_0_31_6_11/WCLK
    SLICE_X46Y47         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11/RAMB/CLK
                         clock pessimism             -0.091    40.527    
                         clock uncertainty           -0.180    40.348    
    SLICE_X46Y47         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.185    40.163    Core_cpu/U_RF/registers_reg_r1_0_31_6_11/RAMB
  -------------------------------------------------------------------
                         required time                         40.163    
                         arrival time                         -28.220    
  -------------------------------------------------------------------
                         slack                                 11.943    

Slack (MET) :             11.982ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r2_0_31_24_29/RAMA_D1/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.449ns  (logic 3.588ns (13.070%)  route 23.861ns (86.930%))
  Logic Levels:           20  (LUT2=1 LUT3=2 LUT5=2 LUT6=9 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.132ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.603ns = ( 40.603 - 40.000 ) 
    Source Clock Delay      (SCD):    0.563ns
    Clock Pessimism Removal (CPR):    -0.171ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.219    -1.779    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.655 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.557    -1.098    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.002 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.565     0.563    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X39Y44         FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y44         FDCE (Prop_fdce_C_Q)         0.456     1.019 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=55, routed)          1.989     3.009    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X15Y37         LUT6 (Prop_lut6_I0_O)        0.124     3.133 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.566    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X15Y37         LUT6 (Prop_lut6_I1_O)        0.124     3.690 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.549     4.239    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X28Y37         LUT5 (Prop_lut5_I2_O)        0.124     4.363 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.803     6.166    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRB0
    SLICE_X42Y44         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.124     6.290 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMB_D1/O
                         net (fo=2, routed)           1.108     7.399    Core_cpu/U_RF/rD20[3]
    SLICE_X37Y42         LUT6 (Prop_lut6_I0_O)        0.124     7.523 r  Core_cpu/U_RF/Mem_DRAM_i_43/O
                         net (fo=261, routed)         0.909     8.432    Core_cpu/U_RF/Bus_wdata[3]
    SLICE_X44Y46         LUT6 (Prop_lut6_I0_O)        0.124     8.556 r  Core_cpu/U_RF/C2_carry_i_14/O
                         net (fo=91, routed)          1.322     9.878    Core_cpu/U_RF/alu_B[3]
    SLICE_X48Y52         LUT6 (Prop_lut6_I2_O)        0.124    10.002 r  Core_cpu/U_RF/Mem_DRAM_i_159/O
                         net (fo=4, routed)           0.857    10.859    Core_cpu/U_RF/Mem_DRAM_i_159_n_0
    SLICE_X48Y56         LUT3 (Prop_lut3_I1_O)        0.124    10.983 f  Core_cpu/U_RF/Mem_DRAM_i_244/O
                         net (fo=5, routed)           1.159    12.141    Core_cpu/U_RF/Mem_DRAM_i_244_n_0
    SLICE_X62Y51         LUT5 (Prop_lut5_I1_O)        0.124    12.265 r  Core_cpu/U_RF/Mem_DRAM_i_176/O
                         net (fo=1, routed)           0.996    13.262    Core_cpu/U_RF/Mem_DRAM_i_176_n_0
    SLICE_X63Y51         LUT6 (Prop_lut6_I1_O)        0.124    13.386 f  Core_cpu/U_RF/Mem_DRAM_i_101/O
                         net (fo=7, routed)           1.353    14.739    Core_cpu/U_RF/Mem_DRAM_i_101_n_0
    SLICE_X41Y43         LUT2 (Prop_lut2_I1_O)        0.124    14.863 r  Core_cpu/U_RF/Mem_DRAM_i_13/O
                         net (fo=8192, routed)        5.318    20.181    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25/A1
    SLICE_X14Y127        RAMS64E (Prop_rams64e_ADR1_O)
                                                      0.272    20.453 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25/RAMS64E_A/O
                         net (fo=1, routed)           0.000    20.453    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25/OA
    SLICE_X14Y127        MUXF7 (Prop_muxf7_I1_O)      0.214    20.667 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25/F7.A/O
                         net (fo=1, routed)           0.000    20.667    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25/O1
    SLICE_X14Y127        MUXF8 (Prop_muxf8_I1_O)      0.088    20.755 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25/F8/O
                         net (fo=1, routed)           1.347    22.102    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11776_12031_25_25_n_0
    SLICE_X28Y116        LUT6 (Prop_lut6_I1_O)        0.319    22.421 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0_i_20/O
                         net (fo=1, routed)           0.000    22.421    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0_i_20_n_0
    SLICE_X28Y116        MUXF7 (Prop_muxf7_I1_O)      0.217    22.638 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0_i_8/O
                         net (fo=1, routed)           0.000    22.638    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0_i_8_n_0
    SLICE_X28Y116        MUXF8 (Prop_muxf8_I1_O)      0.094    22.732 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0_i_2/O
                         net (fo=1, routed)           1.299    24.031    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0_i_2_n_0
    SLICE_X28Y95         LUT6 (Prop_lut6_I1_O)        0.316    24.347 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[25]_INST_0/O
                         net (fo=1, routed)           2.246    26.593    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1[25]
    SLICE_X33Y55         LUT3 (Prop_lut3_I2_O)        0.124    26.717 r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_8/O
                         net (fo=1, routed)           0.824    27.542    Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_8_n_0
    SLICE_X33Y56         LUT6 (Prop_lut6_I4_O)        0.124    27.666 r  Core_cpu/U_RF/registers_reg_r1_0_31_24_29_i_1/O
                         net (fo=2, routed)           0.347    28.012    Core_cpu/U_RF/registers_reg_r2_0_31_24_29/DIA1
    SLICE_X34Y56         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_24_29/RAMA_D1/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.969    38.478    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.578 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.501    39.078    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.169 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.434    40.603    Core_cpu/U_RF/registers_reg_r2_0_31_24_29/WCLK
    SLICE_X34Y56         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_24_29/RAMA_D1/CLK
                         clock pessimism             -0.171    40.432    
                         clock uncertainty           -0.180    40.252    
    SLICE_X34Y56         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.258    39.994    Core_cpu/U_RF/registers_reg_r2_0_31_24_29/RAMA_D1
  -------------------------------------------------------------------
                         required time                         39.994    
                         arrival time                         -28.012    
  -------------------------------------------------------------------
                         slack                                 11.982    

Slack (MET) :             12.090ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[5]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r2_0_31_6_11/RAMB/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.508ns  (logic 4.217ns (15.330%)  route 23.291ns (84.670%))
  Logic Levels:           20  (LUT2=1 LUT3=2 LUT5=1 LUT6=10 MUXF7=2 MUXF8=2 RAMD32=1 RAMS64E=1)
  Clock Path Skew:        -0.037ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.618ns = ( 40.618 - 40.000 ) 
    Source Clock Delay      (SCD):    0.563ns
    Clock Pessimism Removal (CPR):    -0.091ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.219    -1.779    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.655 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.557    -1.098    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.002 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.565     0.563    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X39Y44         FDCE                                         r  Core_cpu/U_PC/pc_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y44         FDCE (Prop_fdce_C_Q)         0.456     1.019 r  Core_cpu/U_PC/pc_reg[5]/Q
                         net (fo=55, routed)          1.989     3.009    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[3]
    SLICE_X15Y37         LUT6 (Prop_lut6_I0_O)        0.124     3.133 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.566    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_2_n_0
    SLICE_X15Y37         LUT6 (Prop_lut6_I1_O)        0.124     3.690 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1/O
                         net (fo=1, routed)           0.549     4.239    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0_i_1_n_0
    SLICE_X28Y37         LUT5 (Prop_lut5_I2_O)        0.124     4.363 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[20]_INST_0/O
                         net (fo=75, routed)          1.803     6.166    Core_cpu/U_RF/registers_reg_r2_0_31_0_5/ADDRB0
    SLICE_X42Y44         RAMD32 (Prop_ramd32_RADR0_O)
                                                      0.124     6.290 r  Core_cpu/U_RF/registers_reg_r2_0_31_0_5/RAMB_D1/O
                         net (fo=2, routed)           1.108     7.399    Core_cpu/U_RF/rD20[3]
    SLICE_X37Y42         LUT6 (Prop_lut6_I0_O)        0.124     7.523 r  Core_cpu/U_RF/Mem_DRAM_i_43/O
                         net (fo=261, routed)         0.909     8.432    Core_cpu/U_RF/Bus_wdata[3]
    SLICE_X44Y46         LUT6 (Prop_lut6_I0_O)        0.124     8.556 r  Core_cpu/U_RF/C2_carry_i_14/O
                         net (fo=91, routed)          1.322     9.878    Core_cpu/U_RF/alu_B[3]
    SLICE_X48Y52         LUT6 (Prop_lut6_I2_O)        0.124    10.002 r  Core_cpu/U_RF/Mem_DRAM_i_159/O
                         net (fo=4, routed)           1.431    11.433    Core_cpu/U_RF/Mem_DRAM_i_159_n_0
    SLICE_X62Y53         LUT3 (Prop_lut3_I2_O)        0.152    11.585 r  Core_cpu/U_RF/Mem_DRAM_i_236/O
                         net (fo=4, routed)           0.522    12.107    Core_cpu/U_RF/Mem_DRAM_i_236_n_0
    SLICE_X62Y51         LUT6 (Prop_lut6_I3_O)        0.326    12.433 r  Core_cpu/U_RF/Mem_DRAM_i_170/O
                         net (fo=1, routed)           0.805    13.238    Core_cpu/U_RF/Mem_DRAM_i_170_n_0
    SLICE_X61Y51         LUT6 (Prop_lut6_I1_O)        0.124    13.362 r  Core_cpu/U_RF/Mem_DRAM_i_100/O
                         net (fo=6, routed)           1.913    15.275    Core_cpu/U_RF/Mem_DRAM_i_100_n_0
    SLICE_X35Y60         LUT2 (Prop_lut2_I0_O)        0.152    15.427 r  Core_cpu/U_RF/Mem_DRAM_i_12/O
                         net (fo=8196, routed)        3.955    19.382    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8/A2
    SLICE_X38Y4          RAMS64E (Prop_rams64e_ADR2_O)
                                                      0.332    19.714 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8/RAMS64E_D/O
                         net (fo=1, routed)           0.000    19.714    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8/OD
    SLICE_X38Y4          MUXF7 (Prop_muxf7_I0_O)      0.241    19.955 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8/F7.B/O
                         net (fo=1, routed)           0.000    19.955    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8/O0
    SLICE_X38Y4          MUXF8 (Prop_muxf8_I0_O)      0.098    20.053 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8/F8/O
                         net (fo=1, routed)           0.859    20.912    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14080_14335_8_8_n_0
    SLICE_X35Y3          LUT6 (Prop_lut6_I0_O)        0.319    21.231 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0_i_14/O
                         net (fo=1, routed)           0.000    21.231    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0_i_14_n_0
    SLICE_X35Y3          MUXF7 (Prop_muxf7_I1_O)      0.245    21.476 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0_i_5/O
                         net (fo=1, routed)           0.000    21.476    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0_i_5_n_0
    SLICE_X35Y3          MUXF8 (Prop_muxf8_I0_O)      0.104    21.580 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0_i_1/O
                         net (fo=1, routed)           1.195    22.775    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0_i_1_n_0
    SLICE_X33Y11         LUT6 (Prop_lut6_I0_O)        0.316    23.091 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[8]_INST_0/O
                         net (fo=1, routed)           2.350    25.441    Core_cpu/U_RF/registers_reg_r1_0_31_30_31_i_1[8]
    SLICE_X33Y55         LUT3 (Prop_lut3_I2_O)        0.152    25.593 r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_21/O
                         net (fo=1, routed)           1.341    26.934    Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_21_n_0
    SLICE_X31Y45         LUT6 (Prop_lut6_I4_O)        0.332    27.266 r  Core_cpu/U_RF/registers_reg_r1_0_31_6_11_i_4/O
                         net (fo=2, routed)           0.805    28.072    Core_cpu/U_RF/registers_reg_r2_0_31_6_11/DIB0
    SLICE_X42Y47         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_6_11/RAMB/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.969    38.478    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.578 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.501    39.078    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.169 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.448    40.618    Core_cpu/U_RF/registers_reg_r2_0_31_6_11/WCLK
    SLICE_X42Y47         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_6_11/RAMB/CLK
                         clock pessimism             -0.091    40.526    
                         clock uncertainty           -0.180    40.347    
    SLICE_X42Y47         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.185    40.162    Core_cpu/U_RF/registers_reg_r2_0_31_6_11/RAMB
  -------------------------------------------------------------------
                         required time                         40.162    
                         arrival time                         -28.072    
  -------------------------------------------------------------------
                         slack                                 12.090    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.168ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][1]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.273ns  (logic 0.186ns (68.109%)  route 0.087ns (31.891%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.090ns
    Source Clock Delay      (SCD):    0.504ns
    Clock Pessimism Removal (CPR):    0.574ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.784    -0.325    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.280 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.205    -0.075    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.049 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.553     0.504    U_Button/cpu_clk_BUFG
    SLICE_X48Y24         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y24         FDCE (Prop_fdce_C_Q)         0.141     0.645 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.087     0.732    U_Button/p_1_in8_in
    SLICE_X49Y24         LUT4 (Prop_lut4_I1_O)        0.045     0.777 r  U_Button/debounce_gen[1].debounce_cnt[1][1]_i_1/O
                         net (fo=1, routed)           0.000     0.777    U_Button/debounce_gen[1].debounce_cnt[1][1]_i_1_n_0
    SLICE_X49Y24         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][1]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.104    -0.043    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.013 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.229     0.242    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.271 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.820     1.090    U_Button/cpu_clk_BUFG
    SLICE_X49Y24         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][1]/C
                         clock pessimism             -0.574     0.517    
    SLICE_X49Y24         FDCE (Hold_fdce_C_D)         0.092     0.609    U_Button/debounce_gen[1].debounce_cnt_reg[1][1]
  -------------------------------------------------------------------
                         required time                         -0.609    
                         arrival time                           0.777    
  -------------------------------------------------------------------
                         slack                                  0.168    

Slack (MET) :             0.239ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][3]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.359ns  (logic 0.186ns (51.797%)  route 0.173ns (48.203%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.090ns
    Source Clock Delay      (SCD):    0.504ns
    Clock Pessimism Removal (CPR):    0.574ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.784    -0.325    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.280 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.205    -0.075    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.049 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.553     0.504    U_Button/cpu_clk_BUFG
    SLICE_X48Y24         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y24         FDCE (Prop_fdce_C_Q)         0.141     0.645 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.173     0.818    U_Button/p_1_in8_in
    SLICE_X49Y24         LUT4 (Prop_lut4_I1_O)        0.045     0.863 r  U_Button/debounce_gen[1].debounce_cnt[1][3]_i_1/O
                         net (fo=1, routed)           0.000     0.863    U_Button/debounce_gen[1].debounce_cnt[1][3]_i_1_n_0
    SLICE_X49Y24         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][3]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.104    -0.043    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.013 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.229     0.242    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.271 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.820     1.090    U_Button/cpu_clk_BUFG
    SLICE_X49Y24         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][3]/C
                         clock pessimism             -0.574     0.517    
    SLICE_X49Y24         FDCE (Hold_fdce_C_D)         0.107     0.624    U_Button/debounce_gen[1].debounce_cnt_reg[1][3]
  -------------------------------------------------------------------
                         required time                         -0.624    
                         arrival time                           0.863    
  -------------------------------------------------------------------
                         slack                                  0.239    

Slack (MET) :             0.244ns  (arrival time - required time)
  Source:                 U_Button/debounce_gen[3].button_stable_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[3].debounce_cnt_reg[3][0]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.351ns  (logic 0.183ns (52.101%)  route 0.168ns (47.899%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.131ns
    Source Clock Delay      (SCD):    0.542ns
    Clock Pessimism Removal (CPR):    0.590ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.784    -0.325    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.280 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.205    -0.075    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.049 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.591     0.542    U_Button/cpu_clk_BUFG
    SLICE_X65Y36         FDCE                                         r  U_Button/debounce_gen[3].button_stable_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X65Y36         FDCE (Prop_fdce_C_Q)         0.141     0.683 r  U_Button/debounce_gen[3].button_stable_reg[3]/Q
                         net (fo=22, routed)          0.168     0.851    U_Button/data0[3]
    SLICE_X65Y36         LUT4 (Prop_lut4_I2_O)        0.042     0.893 r  U_Button/debounce_gen[3].debounce_cnt[3][0]_i_1/O
                         net (fo=1, routed)           0.000     0.893    U_Button/debounce_gen[3].debounce_cnt[3][0]_i_1_n_0
    SLICE_X65Y36         FDCE                                         r  U_Button/debounce_gen[3].debounce_cnt_reg[3][0]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.104    -0.043    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.013 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.229     0.242    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.271 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.861     1.131    U_Button/cpu_clk_BUFG
    SLICE_X65Y36         FDCE                                         r  U_Button/debounce_gen[3].debounce_cnt_reg[3][0]/C
                         clock pessimism             -0.590     0.542    
    SLICE_X65Y36         FDCE (Hold_fdce_C_D)         0.107     0.649    U_Button/debounce_gen[3].debounce_cnt_reg[3][0]
  -------------------------------------------------------------------
                         required time                         -0.649    
                         arrival time                           0.893    
  -------------------------------------------------------------------
                         slack                                  0.244    

Slack (MET) :             0.245ns  (arrival time - required time)
  Source:                 U_Button/button_sync1_reg[4]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/button_sync2_reg[4]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.311ns  (logic 0.141ns (45.298%)  route 0.170ns (54.702%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.136ns
    Source Clock Delay      (SCD):    0.546ns
    Clock Pessimism Removal (CPR):    0.591ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.784    -0.325    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.280 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.205    -0.075    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.049 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.595     0.546    U_Button/cpu_clk_BUFG
    SLICE_X65Y43         FDCE                                         r  U_Button/button_sync1_reg[4]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X65Y43         FDCE (Prop_fdce_C_Q)         0.141     0.687 r  U_Button/button_sync1_reg[4]/Q
                         net (fo=1, routed)           0.170     0.857    U_Button/button_sync1[4]
    SLICE_X65Y43         FDCE                                         r  U_Button/button_sync2_reg[4]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.104    -0.043    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.013 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.229     0.242    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.271 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.866     1.136    U_Button/cpu_clk_BUFG
    SLICE_X65Y43         FDCE                                         r  U_Button/button_sync2_reg[4]/C
                         clock pessimism             -0.591     0.546    
    SLICE_X65Y43         FDCE (Hold_fdce_C_D)         0.066     0.612    U_Button/button_sync2_reg[4]
  -------------------------------------------------------------------
                         required time                         -0.612    
                         arrival time                           0.857    
  -------------------------------------------------------------------
                         slack                                  0.245    

Slack (MET) :             0.249ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][0]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.369ns  (logic 0.189ns (51.279%)  route 0.180ns (48.721%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.090ns
    Source Clock Delay      (SCD):    0.504ns
    Clock Pessimism Removal (CPR):    0.574ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.784    -0.325    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.280 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.205    -0.075    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.049 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.553     0.504    U_Button/cpu_clk_BUFG
    SLICE_X48Y24         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y24         FDCE (Prop_fdce_C_Q)         0.141     0.645 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.180     0.824    U_Button/p_1_in8_in
    SLICE_X49Y24         LUT4 (Prop_lut4_I1_O)        0.048     0.872 r  U_Button/debounce_gen[1].debounce_cnt[1][0]_i_1/O
                         net (fo=1, routed)           0.000     0.872    U_Button/debounce_gen[1].debounce_cnt[1][0]_i_1_n_0
    SLICE_X49Y24         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][0]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.104    -0.043    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.013 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.229     0.242    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.271 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.820     1.090    U_Button/cpu_clk_BUFG
    SLICE_X49Y24         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][0]/C
                         clock pessimism             -0.574     0.517    
    SLICE_X49Y24         FDCE (Hold_fdce_C_D)         0.107     0.624    U_Button/debounce_gen[1].debounce_cnt_reg[1][0]
  -------------------------------------------------------------------
                         required time                         -0.624    
                         arrival time                           0.872    
  -------------------------------------------------------------------
                         slack                                  0.249    

Slack (MET) :             0.254ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][2]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.359ns  (logic 0.186ns (51.797%)  route 0.173ns (48.203%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.090ns
    Source Clock Delay      (SCD):    0.504ns
    Clock Pessimism Removal (CPR):    0.574ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.784    -0.325    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.280 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.205    -0.075    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.049 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.553     0.504    U_Button/cpu_clk_BUFG
    SLICE_X48Y24         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y24         FDCE (Prop_fdce_C_Q)         0.141     0.645 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.173     0.818    U_Button/p_1_in8_in
    SLICE_X49Y24         LUT4 (Prop_lut4_I1_O)        0.045     0.863 r  U_Button/debounce_gen[1].debounce_cnt[1][2]_i_1/O
                         net (fo=1, routed)           0.000     0.863    U_Button/debounce_gen[1].debounce_cnt[1][2]_i_1_n_0
    SLICE_X49Y24         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][2]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.104    -0.043    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.013 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.229     0.242    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.271 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.820     1.090    U_Button/cpu_clk_BUFG
    SLICE_X49Y24         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][2]/C
                         clock pessimism             -0.574     0.517    
    SLICE_X49Y24         FDCE (Hold_fdce_C_D)         0.092     0.609    U_Button/debounce_gen[1].debounce_cnt_reg[1][2]
  -------------------------------------------------------------------
                         required time                         -0.609    
                         arrival time                           0.863    
  -------------------------------------------------------------------
                         slack                                  0.254    

Slack (MET) :             0.260ns  (arrival time - required time)
  Source:                 U_Dig/scan_cnt_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Dig/scan_cnt_reg[2]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.367ns  (logic 0.183ns (49.863%)  route 0.184ns (50.137%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.130ns
    Source Clock Delay      (SCD):    0.542ns
    Clock Pessimism Removal (CPR):    0.589ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.784    -0.325    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.280 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.205    -0.075    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.049 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.591     0.542    U_Dig/cpu_clk_BUFG
    SLICE_X59Y37         FDCE                                         r  U_Dig/scan_cnt_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X59Y37         FDCE (Prop_fdce_C_Q)         0.141     0.683 r  U_Dig/scan_cnt_reg[1]/Q
                         net (fo=18, routed)          0.184     0.867    U_Dig/scan_cnt[1]
    SLICE_X59Y37         LUT4 (Prop_lut4_I1_O)        0.042     0.909 r  U_Dig/scan_cnt[2]_i_1/O
                         net (fo=1, routed)           0.000     0.909    U_Dig/scan_cnt[2]_i_1_n_0
    SLICE_X59Y37         FDCE                                         r  U_Dig/scan_cnt_reg[2]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.104    -0.043    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.013 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.229     0.242    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.271 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.860     1.130    U_Dig/cpu_clk_BUFG
    SLICE_X59Y37         FDCE                                         r  U_Dig/scan_cnt_reg[2]/C
                         clock pessimism             -0.589     0.542    
    SLICE_X59Y37         FDCE (Hold_fdce_C_D)         0.107     0.649    U_Dig/scan_cnt_reg[2]
  -------------------------------------------------------------------
                         required time                         -0.649    
                         arrival time                           0.909    
  -------------------------------------------------------------------
                         slack                                  0.260    

Slack (MET) :             0.262ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].button_stable_reg[1]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.366ns  (logic 0.186ns (50.879%)  route 0.180ns (49.121%))
  Logic Levels:           1  (LUT3=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.090ns
    Source Clock Delay      (SCD):    0.504ns
    Clock Pessimism Removal (CPR):    0.574ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.784    -0.325    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.280 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.205    -0.075    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.049 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.553     0.504    U_Button/cpu_clk_BUFG
    SLICE_X48Y24         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y24         FDCE (Prop_fdce_C_Q)         0.141     0.645 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.180     0.824    U_Button/p_1_in8_in
    SLICE_X49Y24         LUT3 (Prop_lut3_I1_O)        0.045     0.869 r  U_Button/debounce_gen[1].button_stable[1]_i_1/O
                         net (fo=1, routed)           0.000     0.869    U_Button/debounce_gen[1].button_stable[1]_i_1_n_0
    SLICE_X49Y24         FDCE                                         r  U_Button/debounce_gen[1].button_stable_reg[1]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.104    -0.043    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.013 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.229     0.242    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.271 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.820     1.090    U_Button/cpu_clk_BUFG
    SLICE_X49Y24         FDCE                                         r  U_Button/debounce_gen[1].button_stable_reg[1]/C
                         clock pessimism             -0.574     0.517    
    SLICE_X49Y24         FDCE (Hold_fdce_C_D)         0.091     0.608    U_Button/debounce_gen[1].button_stable_reg[1]
  -------------------------------------------------------------------
                         required time                         -0.608    
                         arrival time                           0.869    
  -------------------------------------------------------------------
                         slack                                  0.262    

Slack (MET) :             0.263ns  (arrival time - required time)
  Source:                 U_Button/debounce_gen[3].button_stable_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[3].button_stable_reg[3]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.354ns  (logic 0.186ns (52.507%)  route 0.168ns (47.493%))
  Logic Levels:           1  (LUT3=1)
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.131ns
    Source Clock Delay      (SCD):    0.542ns
    Clock Pessimism Removal (CPR):    0.590ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.784    -0.325    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.280 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.205    -0.075    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.049 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.591     0.542    U_Button/cpu_clk_BUFG
    SLICE_X65Y36         FDCE                                         r  U_Button/debounce_gen[3].button_stable_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X65Y36         FDCE (Prop_fdce_C_Q)         0.141     0.683 r  U_Button/debounce_gen[3].button_stable_reg[3]/Q
                         net (fo=22, routed)          0.168     0.851    U_Button/data0[3]
    SLICE_X65Y36         LUT3 (Prop_lut3_I2_O)        0.045     0.896 r  U_Button/debounce_gen[3].button_stable[3]_i_1/O
                         net (fo=1, routed)           0.000     0.896    U_Button/debounce_gen[3].button_stable[3]_i_1_n_0
    SLICE_X65Y36         FDCE                                         r  U_Button/debounce_gen[3].button_stable_reg[3]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.104    -0.043    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.013 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.229     0.242    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.271 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.861     1.131    U_Button/cpu_clk_BUFG
    SLICE_X65Y36         FDCE                                         r  U_Button/debounce_gen[3].button_stable_reg[3]/C
                         clock pessimism             -0.590     0.542    
    SLICE_X65Y36         FDCE (Hold_fdce_C_D)         0.091     0.633    U_Button/debounce_gen[3].button_stable_reg[3]
  -------------------------------------------------------------------
                         required time                         -0.633    
                         arrival time                           0.896    
  -------------------------------------------------------------------
                         slack                                  0.263    

Slack (MET) :             0.269ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][5]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.409ns  (logic 0.189ns (46.191%)  route 0.220ns (53.809%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.033ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.090ns
    Source Clock Delay      (SCD):    0.504ns
    Clock Pessimism Removal (CPR):    0.554ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.784    -0.325    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.280 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.205    -0.075    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.049 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.553     0.504    U_Button/cpu_clk_BUFG
    SLICE_X48Y24         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y24         FDCE (Prop_fdce_C_Q)         0.141     0.645 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.220     0.865    U_Button/p_1_in8_in
    SLICE_X49Y25         LUT4 (Prop_lut4_I1_O)        0.048     0.913 r  U_Button/debounce_gen[1].debounce_cnt[1][5]_i_1/O
                         net (fo=1, routed)           0.000     0.913    U_Button/debounce_gen[1].debounce_cnt[1][5]_i_1_n_0
    SLICE_X49Y25         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][5]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.104    -0.043    pll_clk
    SLICE_X35Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.013 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.229     0.242    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.271 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.820     1.090    U_Button/cpu_clk_BUFG
    SLICE_X49Y25         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][5]/C
                         clock pessimism             -0.554     0.537    
    SLICE_X49Y25         FDCE (Hold_fdce_C_D)         0.107     0.644    U_Button/debounce_gen[1].debounce_cnt_reg[1][5]
  -------------------------------------------------------------------
                         required time                         -0.644    
                         arrival time                           0.913    
  -------------------------------------------------------------------
                         slack                                  0.269    





Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clk_out1_cpuclk
Waveform(ns):       { 0.000 20.000 }
Period(ns):         40.000
Sources:            { Clkgen/inst/plle2_adv_inst/CLKOUT0 }

Check Type        Corner  Lib Pin            Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     BUFG/I             n/a            2.155         40.000      37.845     BUFGCTRL_X0Y2   Clkgen/inst/clkout1_buf/I
Min Period        n/a     BUFG/I             n/a            2.155         40.000      37.845     BUFGCTRL_X0Y0   cpu_clk_BUFG_inst/I
Min Period        n/a     PLLE2_ADV/CLKOUT0  n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKOUT0
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X37Y42    Core_cpu/U_PC/pc_reg[0]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X39Y45    Core_cpu/U_PC/pc_reg[10]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X39Y45    Core_cpu/U_PC/pc_reg[11]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X39Y47    Core_cpu/U_PC/pc_reg[12]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X41Y46    Core_cpu/U_PC/pc_reg[13]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X41Y46    Core_cpu/U_PC/pc_reg[14]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X41Y46    Core_cpu/U_PC/pc_reg[15]/C
Max Period        n/a     PLLE2_ADV/CLKOUT0  n/a            160.000       40.000      120.000    PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKOUT0
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X46Y130   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_31_31/RAMS64E_B/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X46Y130   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_31_31/RAMS64E_C/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X46Y130   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_31_31/RAMS64E_D/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X8Y50     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_4_4/RAMS64E_D/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X54Y50    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14336_14591_14_14/RAMS64E_A/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X54Y50    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14336_14591_14_14/RAMS64E_B/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X54Y50    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14336_14591_14_14/RAMS64E_C/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X54Y50    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14336_14591_14_14/RAMS64E_D/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X38Y79    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_15360_15615_26_26/RAMS64E_A/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X38Y79    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_15360_15615_26_26/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y47    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_2_2/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y47    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_2_2/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y47    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_2_2/RAMS64E_C/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y47    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_2_2/RAMS64E_D/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X14Y109   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_30_30/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X54Y64    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_10_10/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X54Y64    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_10_10/RAMS64E_C/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X54Y64    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_10_10/RAMS64E_D/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X46Y40    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_11_11/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X46Y40    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_11_11/RAMS64E_B/CLK



---------------------------------------------------------------------------------------------------
From Clock:  clkfbout_cpuclk
  To Clock:  clkfbout_cpuclk

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack       12.633ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clkfbout_cpuclk
Waveform(ns):       { 0.000 20.000 }
Period(ns):         40.000
Sources:            { Clkgen/inst/plle2_adv_inst/CLKFBOUT }

Check Type  Corner  Lib Pin             Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period  n/a     BUFG/I              n/a            2.155         40.000      37.845     BUFGCTRL_X0Y1   Clkgen/inst/clkf_buf/I
Min Period  n/a     PLLE2_ADV/CLKFBOUT  n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBOUT
Min Period  n/a     PLLE2_ADV/CLKFBIN   n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBIN
Max Period  n/a     PLLE2_ADV/CLKFBIN   n/a            52.633        40.000      12.633     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBIN
Max Period  n/a     PLLE2_ADV/CLKFBOUT  n/a            160.000       40.000      120.000    PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBOUT



