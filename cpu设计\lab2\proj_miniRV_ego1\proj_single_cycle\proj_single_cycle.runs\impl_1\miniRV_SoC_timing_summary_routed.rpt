Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Fri Jul  4 10:24:29 2025
| Host         : L running 64-bit major release  (build 9200)
| Command      : report_timing_summary -max_paths 10 -file miniRV_SoC_timing_summary_routed.rpt -pb miniRV_SoC_timing_summary_routed.pb -rpx miniRV_SoC_timing_summary_routed.rpx -warn_on_violation
| Design       : miniRV_SoC
| Device       : 7a35t-csg324
| Speed File   : -1  PRODUCTION 1.23 2018-06-13
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Timing Summary Report

------------------------------------------------------------------------------------------------
| Timer Settings
| --------------
------------------------------------------------------------------------------------------------

  Enable Multi Corner Analysis               :  Yes
  Enable Pessimism Removal                   :  Yes
  Pessimism Removal Resolution               :  Nearest Common Node
  Enable Input Delay Default Clock           :  No
  Enable Preset / Clear Arcs                 :  No
  Disable Flight Delays                      :  No
  Ignore I/O Paths                           :  No
  Timing Early Launch at Borrowing Latches   :  false

  Corner  Analyze    Analyze    
  Name    Max Paths  Min Paths  
  ------  ---------  ---------  
  Slow    Yes        Yes        
  Fast    Yes        Yes        



check_timing report

Table of Contents
-----------------
1. checking no_clock
2. checking constant_clock
3. checking pulse_width_clock
4. checking unconstrained_internal_endpoints
5. checking no_input_delay
6. checking no_output_delay
7. checking multiple_clock
8. checking generated_clocks
9. checking loops
10. checking partial_input_delay
11. checking partial_output_delay
12. checking latch_loops

1. checking no_clock
--------------------
 There are 0 register/latch pins with no clock.


2. checking constant_clock
--------------------------
 There are 0 register/latch pins with constant_clock.


3. checking pulse_width_clock
-----------------------------
 There are 0 register/latch pins which need pulse_width check


4. checking unconstrained_internal_endpoints
--------------------------------------------
 There are 0 pins that are not constrained for maximum delay.

 There are 0 pins that are not constrained for maximum delay due to constant clock.


5. checking no_input_delay
--------------------------
 There are 22 input ports with no input delay specified. (HIGH)

 There are 0 input ports with no input delay but user has a false path constraint.


6. checking no_output_delay
---------------------------
 There are 38 ports with no output delay specified. (HIGH)

 There are 0 ports with no output delay but user has a false path constraint

 There are 0 ports with no output delay but with a timing clock defined on it or propagating through it


7. checking multiple_clock
--------------------------
 There are 0 register/latch pins with multiple clocks.


8. checking generated_clocks
----------------------------
 There are 0 generated clocks that are not connected to a clock source.


9. checking loops
-----------------
 There are 0 combinational loops in the design.


10. checking partial_input_delay
--------------------------------
 There are 0 input ports with partial input delay specified.


11. checking partial_output_delay
---------------------------------
 There are 0 ports with partial output delay specified.


12. checking latch_loops
------------------------
 There are 0 combinational latch loops in the design through latch input



------------------------------------------------------------------------------------------------
| Design Timing Summary
| ---------------------
------------------------------------------------------------------------------------------------

    WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
    -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
     12.061        0.000                      0                82977        0.182        0.000                      0                82977        3.000        0.000                       0                  8605  


All user specified timing constraints are met.


------------------------------------------------------------------------------------------------
| Clock Summary
| -------------
------------------------------------------------------------------------------------------------

Clock              Waveform(ns)       Period(ns)      Frequency(MHz)
-----              ------------       ----------      --------------
fpga_clk           {0.000 5.000}      10.000          100.000         
  clk_out1_cpuclk  {0.000 20.000}     40.000          25.000          
  clkfbout_cpuclk  {0.000 20.000}     40.000          25.000          


------------------------------------------------------------------------------------------------
| Intra Clock Table
| -----------------
------------------------------------------------------------------------------------------------

Clock                  WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
-----                  -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
fpga_clk                                                                                                                                                             3.000        0.000                       0                     1  
  clk_out1_cpuclk       12.061        0.000                      0                82977        0.182        0.000                      0                82977       18.750        0.000                       0                  8601  
  clkfbout_cpuclk                                                                                                                                                   12.633        0.000                       0                     3  


------------------------------------------------------------------------------------------------
| Inter Clock Table
| -----------------
------------------------------------------------------------------------------------------------

From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  


------------------------------------------------------------------------------------------------
| Other Path Groups Table
| -----------------------
------------------------------------------------------------------------------------------------

Path Group    From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    ----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  


------------------------------------------------------------------------------------------------
| Timing Details
| --------------
------------------------------------------------------------------------------------------------


---------------------------------------------------------------------------------------------------
From Clock:  fpga_clk
  To Clock:  fpga_clk

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack        3.000ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         fpga_clk
Waveform(ns):       { 0.000 5.000 }
Period(ns):         10.000
Sources:            { fpga_clk }

Check Type        Corner  Lib Pin           Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     PLLE2_ADV/CLKIN1  n/a            1.249         10.000      8.751      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Max Period        n/a     PLLE2_ADV/CLKIN1  n/a            52.633        10.000      42.633     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Low Pulse Width   Slow    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Low Pulse Width   Fast    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
High Pulse Width  Slow    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
High Pulse Width  Fast    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1



---------------------------------------------------------------------------------------------------
From Clock:  clk_out1_cpuclk
  To Clock:  clk_out1_cpuclk

Setup :            0  Failing Endpoints,  Worst Slack       12.061ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.182ns,  Total Violation        0.000ns
PW    :            0  Failing Endpoints,  Worst Slack       18.750ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             12.061ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r2_0_31_12_17/RAMC/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.476ns  (logic 3.267ns (11.890%)  route 24.209ns (88.110%))
  Logic Levels:           18  (LUT2=1 LUT4=1 LUT5=3 LUT6=8 MUXF7=2 MUXF8=2 RAMS64E=1)
  Clock Path Skew:        -0.108ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.498ns = ( 40.498 - 40.000 ) 
    Source Clock Delay      (SCD):    0.403ns
    Clock Pessimism Removal (CPR):    -0.203ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.057    -1.941    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.817 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.567    -1.249    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.153 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.556     0.403    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X47Y51         FDCE                                         r  Core_cpu/U_PC/pc_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y51         FDCE (Prop_fdce_C_Q)         0.456     0.859 r  Core_cpu/U_PC/pc_reg[3]/Q
                         net (fo=55, routed)          2.245     3.104    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[1]
    SLICE_X59Y67         LUT6 (Prop_lut6_I2_O)        0.124     3.228 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.660    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2_n_0
    SLICE_X59Y67         LUT6 (Prop_lut6_I1_O)        0.124     3.784 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1/O
                         net (fo=1, routed)           0.494     4.279    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1_n_0
    SLICE_X58Y65         LUT5 (Prop_lut5_I2_O)        0.124     4.403 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0/O
                         net (fo=17, routed)          1.429     5.832    Core_cpu/U_PC/spo[5]
    SLICE_X48Y62         LUT5 (Prop_lut5_I2_O)        0.124     5.956 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.159     6.114    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X48Y62         LUT5 (Prop_lut5_I4_O)        0.124     6.238 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.850     7.088    Core_cpu/U_PC/sext_op[2]
    SLICE_X55Y58         LUT2 (Prop_lut2_I1_O)        0.124     7.212 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.001     8.213    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X44Y52         LUT6 (Prop_lut6_I5_O)        0.124     8.337 r  Core_cpu/U_RF/npc0_carry_i_8/O
                         net (fo=7, routed)           0.906     9.244    Core_cpu/U_RF/npc0_carry_i_6[0]
    SLICE_X33Y43         LUT4 (Prop_lut4_I0_O)        0.124     9.368 r  Core_cpu/U_RF/C2_carry_i_16/O
                         net (fo=46, routed)          2.315    11.683    Core_cpu/U_RF/Mem_DRAM_i_41_0[0]
    SLICE_X15Y54         LUT6 (Prop_lut6_I4_O)        0.124    11.807 r  Core_cpu/U_RF/Mem_DRAM_i_85/O
                         net (fo=1, routed)           1.079    12.886    Core_cpu/U_ALU/pc_reg[7]_1
    SLICE_X29Y46         LUT6 (Prop_lut6_I4_O)        0.124    13.010 r  Core_cpu/U_ALU/Mem_DRAM_i_9/O
                         net (fo=8197, routed)        5.799    18.809    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16/A5
    SLICE_X54Y136        RAMS64E (Prop_rams64e_ADR5_O)
                                                      0.124    18.933 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16/RAMS64E_D/O
                         net (fo=1, routed)           0.000    18.933    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16/OD
    SLICE_X54Y136        MUXF7 (Prop_muxf7_I0_O)      0.241    19.174 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16/F7.B/O
                         net (fo=1, routed)           0.000    19.174    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16/O0
    SLICE_X54Y136        MUXF8 (Prop_muxf8_I0_O)      0.098    19.272 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16/F8/O
                         net (fo=1, routed)           1.552    20.823    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16_n_0
    SLICE_X39Y133        LUT6 (Prop_lut6_I1_O)        0.319    21.142 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0_i_14/O
                         net (fo=1, routed)           0.000    21.142    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0_i_14_n_0
    SLICE_X39Y133        MUXF7 (Prop_muxf7_I1_O)      0.245    21.387 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0_i_5/O
                         net (fo=1, routed)           0.000    21.387    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0_i_5_n_0
    SLICE_X39Y133        MUXF8 (Prop_muxf8_I0_O)      0.104    21.491 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0_i_1/O
                         net (fo=1, routed)           1.344    22.835    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0_i_1_n_0
    SLICE_X47Y127        LUT6 (Prop_lut6_I0_O)        0.316    23.151 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0/O
                         net (fo=1, routed)           3.933    27.084    Core_cpu/U_RF/registers_reg_r2_0_31_30_31_0[16]
    SLICE_X44Y52         LUT6 (Prop_lut6_I2_O)        0.124    27.208 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_6/O
                         net (fo=2, routed)           0.671    27.879    Core_cpu/U_RF/registers_reg_r2_0_31_12_17/DIC0
    SLICE_X42Y49         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_12_17/RAMC/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.839    38.348    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.448 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.511    38.959    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.050 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.448    40.498    Core_cpu/U_RF/registers_reg_r2_0_31_12_17/WCLK
    SLICE_X42Y49         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r2_0_31_12_17/RAMC/CLK
                         clock pessimism             -0.203    40.295    
                         clock uncertainty           -0.180    40.115    
    SLICE_X42Y49         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.175    39.940    Core_cpu/U_RF/registers_reg_r2_0_31_12_17/RAMC
  -------------------------------------------------------------------
                         required time                         39.940    
                         arrival time                         -27.879    
  -------------------------------------------------------------------
                         slack                                 12.061    

Slack (MET) :             12.091ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Core_cpu/U_RF/registers_reg_r1_0_31_12_17/RAMC/I
                            (rising edge-triggered cell RAMD32 clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.513ns  (logic 3.267ns (11.874%)  route 24.246ns (88.126%))
  Logic Levels:           18  (LUT2=1 LUT4=1 LUT5=3 LUT6=8 MUXF7=2 MUXF8=2 RAMS64E=1)
  Clock Path Skew:        -0.042ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.486ns = ( 40.486 - 40.000 ) 
    Source Clock Delay      (SCD):    0.403ns
    Clock Pessimism Removal (CPR):    -0.125ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.057    -1.941    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.817 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.567    -1.249    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.153 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.556     0.403    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X47Y51         FDCE                                         r  Core_cpu/U_PC/pc_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y51         FDCE (Prop_fdce_C_Q)         0.456     0.859 r  Core_cpu/U_PC/pc_reg[3]/Q
                         net (fo=55, routed)          2.245     3.104    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[1]
    SLICE_X59Y67         LUT6 (Prop_lut6_I2_O)        0.124     3.228 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.660    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2_n_0
    SLICE_X59Y67         LUT6 (Prop_lut6_I1_O)        0.124     3.784 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1/O
                         net (fo=1, routed)           0.494     4.279    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1_n_0
    SLICE_X58Y65         LUT5 (Prop_lut5_I2_O)        0.124     4.403 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0/O
                         net (fo=17, routed)          1.429     5.832    Core_cpu/U_PC/spo[5]
    SLICE_X48Y62         LUT5 (Prop_lut5_I2_O)        0.124     5.956 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.159     6.114    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X48Y62         LUT5 (Prop_lut5_I4_O)        0.124     6.238 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.850     7.088    Core_cpu/U_PC/sext_op[2]
    SLICE_X55Y58         LUT2 (Prop_lut2_I1_O)        0.124     7.212 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.001     8.213    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X44Y52         LUT6 (Prop_lut6_I5_O)        0.124     8.337 r  Core_cpu/U_RF/npc0_carry_i_8/O
                         net (fo=7, routed)           0.906     9.244    Core_cpu/U_RF/npc0_carry_i_6[0]
    SLICE_X33Y43         LUT4 (Prop_lut4_I0_O)        0.124     9.368 r  Core_cpu/U_RF/C2_carry_i_16/O
                         net (fo=46, routed)          2.315    11.683    Core_cpu/U_RF/Mem_DRAM_i_41_0[0]
    SLICE_X15Y54         LUT6 (Prop_lut6_I4_O)        0.124    11.807 r  Core_cpu/U_RF/Mem_DRAM_i_85/O
                         net (fo=1, routed)           1.079    12.886    Core_cpu/U_ALU/pc_reg[7]_1
    SLICE_X29Y46         LUT6 (Prop_lut6_I4_O)        0.124    13.010 r  Core_cpu/U_ALU/Mem_DRAM_i_9/O
                         net (fo=8197, routed)        5.799    18.809    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16/A5
    SLICE_X54Y136        RAMS64E (Prop_rams64e_ADR5_O)
                                                      0.124    18.933 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16/RAMS64E_D/O
                         net (fo=1, routed)           0.000    18.933    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16/OD
    SLICE_X54Y136        MUXF7 (Prop_muxf7_I0_O)      0.241    19.174 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16/F7.B/O
                         net (fo=1, routed)           0.000    19.174    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16/O0
    SLICE_X54Y136        MUXF8 (Prop_muxf8_I0_O)      0.098    19.272 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16/F8/O
                         net (fo=1, routed)           1.552    20.823    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13824_14079_16_16_n_0
    SLICE_X39Y133        LUT6 (Prop_lut6_I1_O)        0.319    21.142 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0_i_14/O
                         net (fo=1, routed)           0.000    21.142    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0_i_14_n_0
    SLICE_X39Y133        MUXF7 (Prop_muxf7_I1_O)      0.245    21.387 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0_i_5/O
                         net (fo=1, routed)           0.000    21.387    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0_i_5_n_0
    SLICE_X39Y133        MUXF8 (Prop_muxf8_I0_O)      0.104    21.491 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0_i_1/O
                         net (fo=1, routed)           1.344    22.835    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0_i_1_n_0
    SLICE_X47Y127        LUT6 (Prop_lut6_I0_O)        0.316    23.151 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/spo[16]_INST_0/O
                         net (fo=1, routed)           3.933    27.084    Core_cpu/U_RF/registers_reg_r2_0_31_30_31_0[16]
    SLICE_X44Y52         LUT6 (Prop_lut6_I2_O)        0.124    27.208 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_6/O
                         net (fo=2, routed)           0.707    27.915    Core_cpu/U_RF/registers_reg_r1_0_31_12_17/DIC0
    SLICE_X38Y50         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17/RAMC/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.839    38.348    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.448 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.511    38.959    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.050 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.436    40.486    Core_cpu/U_RF/registers_reg_r1_0_31_12_17/WCLK
    SLICE_X38Y50         RAMD32                                       r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17/RAMC/CLK
                         clock pessimism             -0.125    40.361    
                         clock uncertainty           -0.180    40.181    
    SLICE_X38Y50         RAMD32 (Setup_ramd32_CLK_I)
                                                     -0.175    40.006    Core_cpu/U_RF/registers_reg_r1_0_31_12_17/RAMC
  -------------------------------------------------------------------
                         required time                         40.006    
                         arrival time                         -27.915    
  -------------------------------------------------------------------
                         slack                                 12.091    

Slack (MET) :             12.126ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_A/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.296ns  (logic 5.097ns (18.673%)  route 22.199ns (81.327%))
  Logic Levels:           23  (CARRY4=5 LUT2=4 LUT3=4 LUT4=1 LUT5=5 LUT6=4)
  Clock Path Skew:        0.134ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.733ns = ( 40.733 - 40.000 ) 
    Source Clock Delay      (SCD):    0.403ns
    Clock Pessimism Removal (CPR):    -0.196ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.057    -1.941    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.817 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.567    -1.249    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.153 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.556     0.403    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X47Y51         FDCE                                         r  Core_cpu/U_PC/pc_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y51         FDCE (Prop_fdce_C_Q)         0.456     0.859 r  Core_cpu/U_PC/pc_reg[3]/Q
                         net (fo=55, routed)          2.245     3.104    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[1]
    SLICE_X59Y67         LUT6 (Prop_lut6_I2_O)        0.124     3.228 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.660    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2_n_0
    SLICE_X59Y67         LUT6 (Prop_lut6_I1_O)        0.124     3.784 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1/O
                         net (fo=1, routed)           0.494     4.279    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1_n_0
    SLICE_X58Y65         LUT5 (Prop_lut5_I2_O)        0.124     4.403 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0/O
                         net (fo=17, routed)          1.429     5.832    Core_cpu/U_PC/spo[5]
    SLICE_X48Y62         LUT5 (Prop_lut5_I2_O)        0.124     5.956 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.159     6.114    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X48Y62         LUT5 (Prop_lut5_I4_O)        0.124     6.238 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.850     7.088    Core_cpu/U_PC/sext_op[2]
    SLICE_X55Y58         LUT2 (Prop_lut2_I1_O)        0.124     7.212 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.293     8.505    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y52         LUT3 (Prop_lut3_I2_O)        0.150     8.655 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.706     9.361    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y53         LUT6 (Prop_lut6_I1_O)        0.326     9.687 r  Core_cpu/U_RF/C2_carry__0_i_20/O
                         net (fo=3, routed)           0.673    10.360    Core_cpu/U_RF/sext_ext[12]
    SLICE_X36Y47         LUT4 (Prop_lut4_I0_O)        0.124    10.484 r  Core_cpu/U_RF/C2_carry__0_i_11/O
                         net (fo=8, routed)           0.649    11.134    Core_cpu/U_RF/alu_B[12]
    SLICE_X33Y50         LUT3 (Prop_lut3_I2_O)        0.124    11.258 r  Core_cpu/U_RF/C0_carry__2_i_8/O
                         net (fo=1, routed)           0.000    11.258    Core_cpu/U_ALU/Mem_DRAM_i_121_0[0]
    SLICE_X33Y50         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.532    11.790 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.790    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X33Y51         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.904 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.904    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X33Y52         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.018 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.018    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X33Y53         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.132 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.132    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X33Y54         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.371 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.963    13.333    Core_cpu/U_ALU/data0[30]
    SLICE_X36Y54         LUT3 (Prop_lut3_I2_O)        0.328    13.661 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.903    14.564    Core_cpu/U_RF/pc_reg[30]
    SLICE_X36Y58         LUT6 (Prop_lut6_I0_O)        0.326    14.890 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.298    16.189    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X44Y58         LUT5 (Prop_lut5_I4_O)        0.152    16.341 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           1.577    17.918    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I1_O)        0.354    18.272 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.281    18.553    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I0_O)        0.332    18.885 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.890    19.775    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X51Y46         LUT2 (Prop_lut2_I1_O)        0.124    19.899 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.659    21.558    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X57Y28         LUT3 (Prop_lut3_I1_O)        0.118    21.676 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2/O
                         net (fo=1, routed)           0.433    22.110    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2_n_0
    SLICE_X57Y28         LUT5 (Prop_lut5_I0_O)        0.326    22.436 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_1/O
                         net (fo=128, routed)         5.263    27.698    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/WE
    SLICE_X6Y101         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_A/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.839    38.348    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.448 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.511    38.959    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.050 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.683    40.733    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/WCLK
    SLICE_X6Y101         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_A/CLK
                         clock pessimism             -0.196    40.537    
                         clock uncertainty           -0.180    40.357    
    SLICE_X6Y101         RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.824    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_A
  -------------------------------------------------------------------
                         required time                         39.824    
                         arrival time                         -27.698    
  -------------------------------------------------------------------
                         slack                                 12.126    

Slack (MET) :             12.126ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_B/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.296ns  (logic 5.097ns (18.673%)  route 22.199ns (81.327%))
  Logic Levels:           23  (CARRY4=5 LUT2=4 LUT3=4 LUT4=1 LUT5=5 LUT6=4)
  Clock Path Skew:        0.134ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.733ns = ( 40.733 - 40.000 ) 
    Source Clock Delay      (SCD):    0.403ns
    Clock Pessimism Removal (CPR):    -0.196ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.057    -1.941    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.817 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.567    -1.249    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.153 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.556     0.403    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X47Y51         FDCE                                         r  Core_cpu/U_PC/pc_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y51         FDCE (Prop_fdce_C_Q)         0.456     0.859 r  Core_cpu/U_PC/pc_reg[3]/Q
                         net (fo=55, routed)          2.245     3.104    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[1]
    SLICE_X59Y67         LUT6 (Prop_lut6_I2_O)        0.124     3.228 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.660    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2_n_0
    SLICE_X59Y67         LUT6 (Prop_lut6_I1_O)        0.124     3.784 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1/O
                         net (fo=1, routed)           0.494     4.279    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1_n_0
    SLICE_X58Y65         LUT5 (Prop_lut5_I2_O)        0.124     4.403 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0/O
                         net (fo=17, routed)          1.429     5.832    Core_cpu/U_PC/spo[5]
    SLICE_X48Y62         LUT5 (Prop_lut5_I2_O)        0.124     5.956 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.159     6.114    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X48Y62         LUT5 (Prop_lut5_I4_O)        0.124     6.238 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.850     7.088    Core_cpu/U_PC/sext_op[2]
    SLICE_X55Y58         LUT2 (Prop_lut2_I1_O)        0.124     7.212 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.293     8.505    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y52         LUT3 (Prop_lut3_I2_O)        0.150     8.655 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.706     9.361    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y53         LUT6 (Prop_lut6_I1_O)        0.326     9.687 r  Core_cpu/U_RF/C2_carry__0_i_20/O
                         net (fo=3, routed)           0.673    10.360    Core_cpu/U_RF/sext_ext[12]
    SLICE_X36Y47         LUT4 (Prop_lut4_I0_O)        0.124    10.484 r  Core_cpu/U_RF/C2_carry__0_i_11/O
                         net (fo=8, routed)           0.649    11.134    Core_cpu/U_RF/alu_B[12]
    SLICE_X33Y50         LUT3 (Prop_lut3_I2_O)        0.124    11.258 r  Core_cpu/U_RF/C0_carry__2_i_8/O
                         net (fo=1, routed)           0.000    11.258    Core_cpu/U_ALU/Mem_DRAM_i_121_0[0]
    SLICE_X33Y50         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.532    11.790 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.790    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X33Y51         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.904 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.904    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X33Y52         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.018 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.018    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X33Y53         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.132 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.132    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X33Y54         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.371 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.963    13.333    Core_cpu/U_ALU/data0[30]
    SLICE_X36Y54         LUT3 (Prop_lut3_I2_O)        0.328    13.661 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.903    14.564    Core_cpu/U_RF/pc_reg[30]
    SLICE_X36Y58         LUT6 (Prop_lut6_I0_O)        0.326    14.890 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.298    16.189    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X44Y58         LUT5 (Prop_lut5_I4_O)        0.152    16.341 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           1.577    17.918    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I1_O)        0.354    18.272 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.281    18.553    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I0_O)        0.332    18.885 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.890    19.775    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X51Y46         LUT2 (Prop_lut2_I1_O)        0.124    19.899 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.659    21.558    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X57Y28         LUT3 (Prop_lut3_I1_O)        0.118    21.676 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2/O
                         net (fo=1, routed)           0.433    22.110    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2_n_0
    SLICE_X57Y28         LUT5 (Prop_lut5_I0_O)        0.326    22.436 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_1/O
                         net (fo=128, routed)         5.263    27.698    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/WE
    SLICE_X6Y101         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_B/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.839    38.348    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.448 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.511    38.959    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.050 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.683    40.733    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/WCLK
    SLICE_X6Y101         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_B/CLK
                         clock pessimism             -0.196    40.537    
                         clock uncertainty           -0.180    40.357    
    SLICE_X6Y101         RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.824    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_B
  -------------------------------------------------------------------
                         required time                         39.824    
                         arrival time                         -27.698    
  -------------------------------------------------------------------
                         slack                                 12.126    

Slack (MET) :             12.126ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_C/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.296ns  (logic 5.097ns (18.673%)  route 22.199ns (81.327%))
  Logic Levels:           23  (CARRY4=5 LUT2=4 LUT3=4 LUT4=1 LUT5=5 LUT6=4)
  Clock Path Skew:        0.134ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.733ns = ( 40.733 - 40.000 ) 
    Source Clock Delay      (SCD):    0.403ns
    Clock Pessimism Removal (CPR):    -0.196ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.057    -1.941    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.817 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.567    -1.249    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.153 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.556     0.403    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X47Y51         FDCE                                         r  Core_cpu/U_PC/pc_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y51         FDCE (Prop_fdce_C_Q)         0.456     0.859 r  Core_cpu/U_PC/pc_reg[3]/Q
                         net (fo=55, routed)          2.245     3.104    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[1]
    SLICE_X59Y67         LUT6 (Prop_lut6_I2_O)        0.124     3.228 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.660    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2_n_0
    SLICE_X59Y67         LUT6 (Prop_lut6_I1_O)        0.124     3.784 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1/O
                         net (fo=1, routed)           0.494     4.279    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1_n_0
    SLICE_X58Y65         LUT5 (Prop_lut5_I2_O)        0.124     4.403 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0/O
                         net (fo=17, routed)          1.429     5.832    Core_cpu/U_PC/spo[5]
    SLICE_X48Y62         LUT5 (Prop_lut5_I2_O)        0.124     5.956 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.159     6.114    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X48Y62         LUT5 (Prop_lut5_I4_O)        0.124     6.238 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.850     7.088    Core_cpu/U_PC/sext_op[2]
    SLICE_X55Y58         LUT2 (Prop_lut2_I1_O)        0.124     7.212 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.293     8.505    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y52         LUT3 (Prop_lut3_I2_O)        0.150     8.655 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.706     9.361    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y53         LUT6 (Prop_lut6_I1_O)        0.326     9.687 r  Core_cpu/U_RF/C2_carry__0_i_20/O
                         net (fo=3, routed)           0.673    10.360    Core_cpu/U_RF/sext_ext[12]
    SLICE_X36Y47         LUT4 (Prop_lut4_I0_O)        0.124    10.484 r  Core_cpu/U_RF/C2_carry__0_i_11/O
                         net (fo=8, routed)           0.649    11.134    Core_cpu/U_RF/alu_B[12]
    SLICE_X33Y50         LUT3 (Prop_lut3_I2_O)        0.124    11.258 r  Core_cpu/U_RF/C0_carry__2_i_8/O
                         net (fo=1, routed)           0.000    11.258    Core_cpu/U_ALU/Mem_DRAM_i_121_0[0]
    SLICE_X33Y50         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.532    11.790 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.790    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X33Y51         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.904 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.904    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X33Y52         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.018 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.018    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X33Y53         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.132 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.132    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X33Y54         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.371 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.963    13.333    Core_cpu/U_ALU/data0[30]
    SLICE_X36Y54         LUT3 (Prop_lut3_I2_O)        0.328    13.661 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.903    14.564    Core_cpu/U_RF/pc_reg[30]
    SLICE_X36Y58         LUT6 (Prop_lut6_I0_O)        0.326    14.890 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.298    16.189    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X44Y58         LUT5 (Prop_lut5_I4_O)        0.152    16.341 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           1.577    17.918    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I1_O)        0.354    18.272 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.281    18.553    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I0_O)        0.332    18.885 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.890    19.775    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X51Y46         LUT2 (Prop_lut2_I1_O)        0.124    19.899 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.659    21.558    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X57Y28         LUT3 (Prop_lut3_I1_O)        0.118    21.676 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2/O
                         net (fo=1, routed)           0.433    22.110    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2_n_0
    SLICE_X57Y28         LUT5 (Prop_lut5_I0_O)        0.326    22.436 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_1/O
                         net (fo=128, routed)         5.263    27.698    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/WE
    SLICE_X6Y101         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_C/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.839    38.348    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.448 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.511    38.959    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.050 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.683    40.733    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/WCLK
    SLICE_X6Y101         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_C/CLK
                         clock pessimism             -0.196    40.537    
                         clock uncertainty           -0.180    40.357    
    SLICE_X6Y101         RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.824    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_C
  -------------------------------------------------------------------
                         required time                         39.824    
                         arrival time                         -27.698    
  -------------------------------------------------------------------
                         slack                                 12.126    

Slack (MET) :             12.126ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_D/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.296ns  (logic 5.097ns (18.673%)  route 22.199ns (81.327%))
  Logic Levels:           23  (CARRY4=5 LUT2=4 LUT3=4 LUT4=1 LUT5=5 LUT6=4)
  Clock Path Skew:        0.134ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.733ns = ( 40.733 - 40.000 ) 
    Source Clock Delay      (SCD):    0.403ns
    Clock Pessimism Removal (CPR):    -0.196ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.057    -1.941    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.817 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.567    -1.249    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.153 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.556     0.403    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X47Y51         FDCE                                         r  Core_cpu/U_PC/pc_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y51         FDCE (Prop_fdce_C_Q)         0.456     0.859 r  Core_cpu/U_PC/pc_reg[3]/Q
                         net (fo=55, routed)          2.245     3.104    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[1]
    SLICE_X59Y67         LUT6 (Prop_lut6_I2_O)        0.124     3.228 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.660    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2_n_0
    SLICE_X59Y67         LUT6 (Prop_lut6_I1_O)        0.124     3.784 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1/O
                         net (fo=1, routed)           0.494     4.279    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1_n_0
    SLICE_X58Y65         LUT5 (Prop_lut5_I2_O)        0.124     4.403 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0/O
                         net (fo=17, routed)          1.429     5.832    Core_cpu/U_PC/spo[5]
    SLICE_X48Y62         LUT5 (Prop_lut5_I2_O)        0.124     5.956 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.159     6.114    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X48Y62         LUT5 (Prop_lut5_I4_O)        0.124     6.238 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.850     7.088    Core_cpu/U_PC/sext_op[2]
    SLICE_X55Y58         LUT2 (Prop_lut2_I1_O)        0.124     7.212 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.293     8.505    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y52         LUT3 (Prop_lut3_I2_O)        0.150     8.655 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.706     9.361    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y53         LUT6 (Prop_lut6_I1_O)        0.326     9.687 r  Core_cpu/U_RF/C2_carry__0_i_20/O
                         net (fo=3, routed)           0.673    10.360    Core_cpu/U_RF/sext_ext[12]
    SLICE_X36Y47         LUT4 (Prop_lut4_I0_O)        0.124    10.484 r  Core_cpu/U_RF/C2_carry__0_i_11/O
                         net (fo=8, routed)           0.649    11.134    Core_cpu/U_RF/alu_B[12]
    SLICE_X33Y50         LUT3 (Prop_lut3_I2_O)        0.124    11.258 r  Core_cpu/U_RF/C0_carry__2_i_8/O
                         net (fo=1, routed)           0.000    11.258    Core_cpu/U_ALU/Mem_DRAM_i_121_0[0]
    SLICE_X33Y50         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.532    11.790 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.790    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X33Y51         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.904 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.904    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X33Y52         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.018 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.018    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X33Y53         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.132 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.132    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X33Y54         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.371 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.963    13.333    Core_cpu/U_ALU/data0[30]
    SLICE_X36Y54         LUT3 (Prop_lut3_I2_O)        0.328    13.661 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.903    14.564    Core_cpu/U_RF/pc_reg[30]
    SLICE_X36Y58         LUT6 (Prop_lut6_I0_O)        0.326    14.890 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.298    16.189    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X44Y58         LUT5 (Prop_lut5_I4_O)        0.152    16.341 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           1.577    17.918    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I1_O)        0.354    18.272 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.281    18.553    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I0_O)        0.332    18.885 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.890    19.775    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X51Y46         LUT2 (Prop_lut2_I1_O)        0.124    19.899 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.659    21.558    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X57Y28         LUT3 (Prop_lut3_I1_O)        0.118    21.676 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2/O
                         net (fo=1, routed)           0.433    22.110    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2_n_0
    SLICE_X57Y28         LUT5 (Prop_lut5_I0_O)        0.326    22.436 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_1/O
                         net (fo=128, routed)         5.263    27.698    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/WE
    SLICE_X6Y101         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_D/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.839    38.348    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.448 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.511    38.959    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.050 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.683    40.733    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/WCLK
    SLICE_X6Y101         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_D/CLK
                         clock pessimism             -0.196    40.537    
                         clock uncertainty           -0.180    40.357    
    SLICE_X6Y101         RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.824    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_29_29/RAMS64E_D
  -------------------------------------------------------------------
                         required time                         39.824    
                         arrival time                         -27.698    
  -------------------------------------------------------------------
                         slack                                 12.126    

Slack (MET) :             12.236ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_A/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.103ns  (logic 5.097ns (18.806%)  route 22.006ns (81.194%))
  Logic Levels:           23  (CARRY4=5 LUT2=4 LUT3=4 LUT4=1 LUT5=5 LUT6=4)
  Clock Path Skew:        0.052ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.651ns = ( 40.651 - 40.000 ) 
    Source Clock Delay      (SCD):    0.403ns
    Clock Pessimism Removal (CPR):    -0.196ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.057    -1.941    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.817 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.567    -1.249    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.153 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.556     0.403    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X47Y51         FDCE                                         r  Core_cpu/U_PC/pc_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y51         FDCE (Prop_fdce_C_Q)         0.456     0.859 r  Core_cpu/U_PC/pc_reg[3]/Q
                         net (fo=55, routed)          2.245     3.104    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[1]
    SLICE_X59Y67         LUT6 (Prop_lut6_I2_O)        0.124     3.228 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.660    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2_n_0
    SLICE_X59Y67         LUT6 (Prop_lut6_I1_O)        0.124     3.784 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1/O
                         net (fo=1, routed)           0.494     4.279    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1_n_0
    SLICE_X58Y65         LUT5 (Prop_lut5_I2_O)        0.124     4.403 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0/O
                         net (fo=17, routed)          1.429     5.832    Core_cpu/U_PC/spo[5]
    SLICE_X48Y62         LUT5 (Prop_lut5_I2_O)        0.124     5.956 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.159     6.114    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X48Y62         LUT5 (Prop_lut5_I4_O)        0.124     6.238 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.850     7.088    Core_cpu/U_PC/sext_op[2]
    SLICE_X55Y58         LUT2 (Prop_lut2_I1_O)        0.124     7.212 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.293     8.505    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y52         LUT3 (Prop_lut3_I2_O)        0.150     8.655 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.706     9.361    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y53         LUT6 (Prop_lut6_I1_O)        0.326     9.687 r  Core_cpu/U_RF/C2_carry__0_i_20/O
                         net (fo=3, routed)           0.673    10.360    Core_cpu/U_RF/sext_ext[12]
    SLICE_X36Y47         LUT4 (Prop_lut4_I0_O)        0.124    10.484 r  Core_cpu/U_RF/C2_carry__0_i_11/O
                         net (fo=8, routed)           0.649    11.134    Core_cpu/U_RF/alu_B[12]
    SLICE_X33Y50         LUT3 (Prop_lut3_I2_O)        0.124    11.258 r  Core_cpu/U_RF/C0_carry__2_i_8/O
                         net (fo=1, routed)           0.000    11.258    Core_cpu/U_ALU/Mem_DRAM_i_121_0[0]
    SLICE_X33Y50         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.532    11.790 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.790    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X33Y51         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.904 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.904    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X33Y52         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.018 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.018    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X33Y53         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.132 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.132    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X33Y54         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.371 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.963    13.333    Core_cpu/U_ALU/data0[30]
    SLICE_X36Y54         LUT3 (Prop_lut3_I2_O)        0.328    13.661 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.903    14.564    Core_cpu/U_RF/pc_reg[30]
    SLICE_X36Y58         LUT6 (Prop_lut6_I0_O)        0.326    14.890 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.298    16.189    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X44Y58         LUT5 (Prop_lut5_I4_O)        0.152    16.341 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           1.577    17.918    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I1_O)        0.354    18.272 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.281    18.553    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I0_O)        0.332    18.885 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.890    19.775    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X51Y46         LUT2 (Prop_lut2_I1_O)        0.124    19.899 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.659    21.558    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X57Y28         LUT3 (Prop_lut3_I1_O)        0.118    21.676 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2/O
                         net (fo=1, routed)           0.433    22.110    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2_n_0
    SLICE_X57Y28         LUT5 (Prop_lut5_I0_O)        0.326    22.436 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_1/O
                         net (fo=128, routed)         5.070    27.506    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/WE
    SLICE_X54Y128        RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_A/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.839    38.348    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.448 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.511    38.959    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.050 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.601    40.651    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/WCLK
    SLICE_X54Y128        RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_A/CLK
                         clock pessimism             -0.196    40.455    
                         clock uncertainty           -0.180    40.275    
    SLICE_X54Y128        RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.742    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_A
  -------------------------------------------------------------------
                         required time                         39.742    
                         arrival time                         -27.506    
  -------------------------------------------------------------------
                         slack                                 12.236    

Slack (MET) :             12.236ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_B/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.103ns  (logic 5.097ns (18.806%)  route 22.006ns (81.194%))
  Logic Levels:           23  (CARRY4=5 LUT2=4 LUT3=4 LUT4=1 LUT5=5 LUT6=4)
  Clock Path Skew:        0.052ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.651ns = ( 40.651 - 40.000 ) 
    Source Clock Delay      (SCD):    0.403ns
    Clock Pessimism Removal (CPR):    -0.196ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.057    -1.941    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.817 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.567    -1.249    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.153 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.556     0.403    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X47Y51         FDCE                                         r  Core_cpu/U_PC/pc_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y51         FDCE (Prop_fdce_C_Q)         0.456     0.859 r  Core_cpu/U_PC/pc_reg[3]/Q
                         net (fo=55, routed)          2.245     3.104    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[1]
    SLICE_X59Y67         LUT6 (Prop_lut6_I2_O)        0.124     3.228 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.660    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2_n_0
    SLICE_X59Y67         LUT6 (Prop_lut6_I1_O)        0.124     3.784 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1/O
                         net (fo=1, routed)           0.494     4.279    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1_n_0
    SLICE_X58Y65         LUT5 (Prop_lut5_I2_O)        0.124     4.403 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0/O
                         net (fo=17, routed)          1.429     5.832    Core_cpu/U_PC/spo[5]
    SLICE_X48Y62         LUT5 (Prop_lut5_I2_O)        0.124     5.956 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.159     6.114    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X48Y62         LUT5 (Prop_lut5_I4_O)        0.124     6.238 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.850     7.088    Core_cpu/U_PC/sext_op[2]
    SLICE_X55Y58         LUT2 (Prop_lut2_I1_O)        0.124     7.212 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.293     8.505    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y52         LUT3 (Prop_lut3_I2_O)        0.150     8.655 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.706     9.361    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y53         LUT6 (Prop_lut6_I1_O)        0.326     9.687 r  Core_cpu/U_RF/C2_carry__0_i_20/O
                         net (fo=3, routed)           0.673    10.360    Core_cpu/U_RF/sext_ext[12]
    SLICE_X36Y47         LUT4 (Prop_lut4_I0_O)        0.124    10.484 r  Core_cpu/U_RF/C2_carry__0_i_11/O
                         net (fo=8, routed)           0.649    11.134    Core_cpu/U_RF/alu_B[12]
    SLICE_X33Y50         LUT3 (Prop_lut3_I2_O)        0.124    11.258 r  Core_cpu/U_RF/C0_carry__2_i_8/O
                         net (fo=1, routed)           0.000    11.258    Core_cpu/U_ALU/Mem_DRAM_i_121_0[0]
    SLICE_X33Y50         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.532    11.790 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.790    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X33Y51         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.904 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.904    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X33Y52         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.018 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.018    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X33Y53         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.132 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.132    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X33Y54         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.371 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.963    13.333    Core_cpu/U_ALU/data0[30]
    SLICE_X36Y54         LUT3 (Prop_lut3_I2_O)        0.328    13.661 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.903    14.564    Core_cpu/U_RF/pc_reg[30]
    SLICE_X36Y58         LUT6 (Prop_lut6_I0_O)        0.326    14.890 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.298    16.189    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X44Y58         LUT5 (Prop_lut5_I4_O)        0.152    16.341 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           1.577    17.918    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I1_O)        0.354    18.272 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.281    18.553    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I0_O)        0.332    18.885 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.890    19.775    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X51Y46         LUT2 (Prop_lut2_I1_O)        0.124    19.899 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.659    21.558    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X57Y28         LUT3 (Prop_lut3_I1_O)        0.118    21.676 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2/O
                         net (fo=1, routed)           0.433    22.110    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2_n_0
    SLICE_X57Y28         LUT5 (Prop_lut5_I0_O)        0.326    22.436 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_1/O
                         net (fo=128, routed)         5.070    27.506    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/WE
    SLICE_X54Y128        RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_B/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.839    38.348    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.448 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.511    38.959    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.050 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.601    40.651    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/WCLK
    SLICE_X54Y128        RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_B/CLK
                         clock pessimism             -0.196    40.455    
                         clock uncertainty           -0.180    40.275    
    SLICE_X54Y128        RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.742    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_B
  -------------------------------------------------------------------
                         required time                         39.742    
                         arrival time                         -27.506    
  -------------------------------------------------------------------
                         slack                                 12.236    

Slack (MET) :             12.236ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_C/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.103ns  (logic 5.097ns (18.806%)  route 22.006ns (81.194%))
  Logic Levels:           23  (CARRY4=5 LUT2=4 LUT3=4 LUT4=1 LUT5=5 LUT6=4)
  Clock Path Skew:        0.052ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.651ns = ( 40.651 - 40.000 ) 
    Source Clock Delay      (SCD):    0.403ns
    Clock Pessimism Removal (CPR):    -0.196ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.057    -1.941    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.817 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.567    -1.249    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.153 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.556     0.403    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X47Y51         FDCE                                         r  Core_cpu/U_PC/pc_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y51         FDCE (Prop_fdce_C_Q)         0.456     0.859 r  Core_cpu/U_PC/pc_reg[3]/Q
                         net (fo=55, routed)          2.245     3.104    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[1]
    SLICE_X59Y67         LUT6 (Prop_lut6_I2_O)        0.124     3.228 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.660    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2_n_0
    SLICE_X59Y67         LUT6 (Prop_lut6_I1_O)        0.124     3.784 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1/O
                         net (fo=1, routed)           0.494     4.279    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1_n_0
    SLICE_X58Y65         LUT5 (Prop_lut5_I2_O)        0.124     4.403 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0/O
                         net (fo=17, routed)          1.429     5.832    Core_cpu/U_PC/spo[5]
    SLICE_X48Y62         LUT5 (Prop_lut5_I2_O)        0.124     5.956 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.159     6.114    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X48Y62         LUT5 (Prop_lut5_I4_O)        0.124     6.238 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.850     7.088    Core_cpu/U_PC/sext_op[2]
    SLICE_X55Y58         LUT2 (Prop_lut2_I1_O)        0.124     7.212 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.293     8.505    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y52         LUT3 (Prop_lut3_I2_O)        0.150     8.655 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.706     9.361    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y53         LUT6 (Prop_lut6_I1_O)        0.326     9.687 r  Core_cpu/U_RF/C2_carry__0_i_20/O
                         net (fo=3, routed)           0.673    10.360    Core_cpu/U_RF/sext_ext[12]
    SLICE_X36Y47         LUT4 (Prop_lut4_I0_O)        0.124    10.484 r  Core_cpu/U_RF/C2_carry__0_i_11/O
                         net (fo=8, routed)           0.649    11.134    Core_cpu/U_RF/alu_B[12]
    SLICE_X33Y50         LUT3 (Prop_lut3_I2_O)        0.124    11.258 r  Core_cpu/U_RF/C0_carry__2_i_8/O
                         net (fo=1, routed)           0.000    11.258    Core_cpu/U_ALU/Mem_DRAM_i_121_0[0]
    SLICE_X33Y50         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.532    11.790 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.790    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X33Y51         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.904 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.904    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X33Y52         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.018 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.018    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X33Y53         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.132 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.132    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X33Y54         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.371 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.963    13.333    Core_cpu/U_ALU/data0[30]
    SLICE_X36Y54         LUT3 (Prop_lut3_I2_O)        0.328    13.661 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.903    14.564    Core_cpu/U_RF/pc_reg[30]
    SLICE_X36Y58         LUT6 (Prop_lut6_I0_O)        0.326    14.890 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.298    16.189    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X44Y58         LUT5 (Prop_lut5_I4_O)        0.152    16.341 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           1.577    17.918    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I1_O)        0.354    18.272 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.281    18.553    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I0_O)        0.332    18.885 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.890    19.775    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X51Y46         LUT2 (Prop_lut2_I1_O)        0.124    19.899 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.659    21.558    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X57Y28         LUT3 (Prop_lut3_I1_O)        0.118    21.676 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2/O
                         net (fo=1, routed)           0.433    22.110    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2_n_0
    SLICE_X57Y28         LUT5 (Prop_lut5_I0_O)        0.326    22.436 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_1/O
                         net (fo=128, routed)         5.070    27.506    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/WE
    SLICE_X54Y128        RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_C/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.839    38.348    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.448 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.511    38.959    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.050 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.601    40.651    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/WCLK
    SLICE_X54Y128        RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_C/CLK
                         clock pessimism             -0.196    40.455    
                         clock uncertainty           -0.180    40.275    
    SLICE_X54Y128        RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.742    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_C
  -------------------------------------------------------------------
                         required time                         39.742    
                         arrival time                         -27.506    
  -------------------------------------------------------------------
                         slack                                 12.236    

Slack (MET) :             12.236ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_D/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.103ns  (logic 5.097ns (18.806%)  route 22.006ns (81.194%))
  Logic Levels:           23  (CARRY4=5 LUT2=4 LUT3=4 LUT4=1 LUT5=5 LUT6=4)
  Clock Path Skew:        0.052ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.651ns = ( 40.651 - 40.000 ) 
    Source Clock Delay      (SCD):    0.403ns
    Clock Pessimism Removal (CPR):    -0.196ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.057    -1.941    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.817 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.567    -1.249    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.153 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.556     0.403    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X47Y51         FDCE                                         r  Core_cpu/U_PC/pc_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y51         FDCE (Prop_fdce_C_Q)         0.456     0.859 r  Core_cpu/U_PC/pc_reg[3]/Q
                         net (fo=55, routed)          2.245     3.104    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[1]
    SLICE_X59Y67         LUT6 (Prop_lut6_I2_O)        0.124     3.228 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.660    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_2_n_0
    SLICE_X59Y67         LUT6 (Prop_lut6_I1_O)        0.124     3.784 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1/O
                         net (fo=1, routed)           0.494     4.279    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0_i_1_n_0
    SLICE_X58Y65         LUT5 (Prop_lut5_I2_O)        0.124     4.403 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[5]_INST_0/O
                         net (fo=17, routed)          1.429     5.832    Core_cpu/U_PC/spo[5]
    SLICE_X48Y62         LUT5 (Prop_lut5_I2_O)        0.124     5.956 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.159     6.114    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X48Y62         LUT5 (Prop_lut5_I4_O)        0.124     6.238 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.850     7.088    Core_cpu/U_PC/sext_op[2]
    SLICE_X55Y58         LUT2 (Prop_lut2_I1_O)        0.124     7.212 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.293     8.505    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y52         LUT3 (Prop_lut3_I2_O)        0.150     8.655 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.706     9.361    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y53         LUT6 (Prop_lut6_I1_O)        0.326     9.687 r  Core_cpu/U_RF/C2_carry__0_i_20/O
                         net (fo=3, routed)           0.673    10.360    Core_cpu/U_RF/sext_ext[12]
    SLICE_X36Y47         LUT4 (Prop_lut4_I0_O)        0.124    10.484 r  Core_cpu/U_RF/C2_carry__0_i_11/O
                         net (fo=8, routed)           0.649    11.134    Core_cpu/U_RF/alu_B[12]
    SLICE_X33Y50         LUT3 (Prop_lut3_I2_O)        0.124    11.258 r  Core_cpu/U_RF/C0_carry__2_i_8/O
                         net (fo=1, routed)           0.000    11.258    Core_cpu/U_ALU/Mem_DRAM_i_121_0[0]
    SLICE_X33Y50         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.532    11.790 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.790    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X33Y51         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.904 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.904    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X33Y52         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.018 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.018    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X33Y53         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.132 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.132    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X33Y54         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.371 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.963    13.333    Core_cpu/U_ALU/data0[30]
    SLICE_X36Y54         LUT3 (Prop_lut3_I2_O)        0.328    13.661 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.903    14.564    Core_cpu/U_RF/pc_reg[30]
    SLICE_X36Y58         LUT6 (Prop_lut6_I0_O)        0.326    14.890 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.298    16.189    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X44Y58         LUT5 (Prop_lut5_I4_O)        0.152    16.341 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           1.577    17.918    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I1_O)        0.354    18.272 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.281    18.553    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X43Y46         LUT2 (Prop_lut2_I0_O)        0.332    18.885 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.890    19.775    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X51Y46         LUT2 (Prop_lut2_I1_O)        0.124    19.899 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.659    21.558    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X57Y28         LUT3 (Prop_lut3_I1_O)        0.118    21.676 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2/O
                         net (fo=1, routed)           0.433    22.110    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_2_n_0
    SLICE_X57Y28         LUT5 (Prop_lut5_I0_O)        0.326    22.436 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_0_0_i_1/O
                         net (fo=128, routed)         5.070    27.506    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/WE
    SLICE_X54Y128        RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_D/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.839    38.348    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.448 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.511    38.959    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.050 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.601    40.651    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/WCLK
    SLICE_X54Y128        RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_D/CLK
                         clock pessimism             -0.196    40.455    
                         clock uncertainty           -0.180    40.275    
    SLICE_X54Y128        RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.742    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9984_10239_17_17/RAMS64E_D
  -------------------------------------------------------------------
                         required time                         39.742    
                         arrival time                         -27.506    
  -------------------------------------------------------------------
                         slack                                 12.236    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.182ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][1]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.287ns  (logic 0.186ns (64.916%)  route 0.101ns (35.084%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.029ns
    Source Clock Delay      (SCD):    0.456ns
    Clock Pessimism Removal (CPR):    0.560ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.723    -0.386    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.341 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.213    -0.127    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.101 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.558     0.456    U_Button/cpu_clk_BUFG
    SLICE_X48Y30         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y30         FDCE (Prop_fdce_C_Q)         0.141     0.597 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.101     0.698    U_Button/p_1_in8_in
    SLICE_X49Y30         LUT4 (Prop_lut4_I0_O)        0.045     0.743 r  U_Button/debounce_gen[1].debounce_cnt[1][1]_i_1/O
                         net (fo=1, routed)           0.000     0.743    U_Button/debounce_gen[1].debounce_cnt[1][1]_i_1_n_0
    SLICE_X49Y30         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][1]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.028    -0.119    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.063 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.237     0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.203 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.826     1.029    U_Button/cpu_clk_BUFG
    SLICE_X49Y30         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][1]/C
                         clock pessimism             -0.560     0.469    
    SLICE_X49Y30         FDCE (Hold_fdce_C_D)         0.092     0.561    U_Button/debounce_gen[1].debounce_cnt_reg[1][1]
  -------------------------------------------------------------------
                         required time                         -0.561    
                         arrival time                           0.743    
  -------------------------------------------------------------------
                         slack                                  0.182    

Slack (MET) :             0.189ns  (arrival time - required time)
  Source:                 U_Timer/counter1_reg[16]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Timer/counter1_reg[20]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.561ns  (logic 0.392ns (69.876%)  route 0.169ns (30.124%))
  Logic Levels:           3  (CARRY4=2 LUT2=1)
  Clock Path Skew:        0.267ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.039ns
    Source Clock Delay      (SCD):    0.467ns
    Clock Pessimism Removal (CPR):    0.305ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.723    -0.386    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.341 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.213    -0.127    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.101 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.569     0.467    U_Timer/cpu_clk_BUFG
    SLICE_X57Y49         FDCE                                         r  U_Timer/counter1_reg[16]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X57Y49         FDCE (Prop_fdce_C_Q)         0.141     0.608 r  U_Timer/counter1_reg[16]/Q
                         net (fo=3, routed)           0.168     0.777    U_Timer/counter1_reg[16]
    SLICE_X57Y49         LUT2 (Prop_lut2_I0_O)        0.045     0.822 r  U_Timer/counter1[16]_i_5/O
                         net (fo=1, routed)           0.000     0.822    U_Timer/counter1[16]_i_5_n_0
    SLICE_X57Y49         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.152     0.974 r  U_Timer/counter1_reg[16]_i_1/CO[3]
                         net (fo=1, routed)           0.001     0.974    U_Timer/counter1_reg[16]_i_1_n_0
    SLICE_X57Y50         CARRY4 (Prop_carry4_CI_O[0])
                                                      0.054     1.028 r  U_Timer/counter1_reg[20]_i_1/O[0]
                         net (fo=1, routed)           0.000     1.028    U_Timer/counter1_reg[20]_i_1_n_7
    SLICE_X57Y50         FDCE                                         r  U_Timer/counter1_reg[20]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.028    -0.119    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.063 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.237     0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.203 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.835     1.039    U_Timer/cpu_clk_BUFG
    SLICE_X57Y50         FDCE                                         r  U_Timer/counter1_reg[20]/C
                         clock pessimism             -0.305     0.734    
    SLICE_X57Y50         FDCE (Hold_fdce_C_D)         0.105     0.839    U_Timer/counter1_reg[20]
  -------------------------------------------------------------------
                         required time                         -0.839    
                         arrival time                           1.028    
  -------------------------------------------------------------------
                         slack                                  0.189    

Slack (MET) :             0.200ns  (arrival time - required time)
  Source:                 U_Timer/counter1_reg[16]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Timer/counter1_reg[22]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.572ns  (logic 0.403ns (70.455%)  route 0.169ns (29.545%))
  Logic Levels:           3  (CARRY4=2 LUT2=1)
  Clock Path Skew:        0.267ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.039ns
    Source Clock Delay      (SCD):    0.467ns
    Clock Pessimism Removal (CPR):    0.305ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.723    -0.386    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.341 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.213    -0.127    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.101 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.569     0.467    U_Timer/cpu_clk_BUFG
    SLICE_X57Y49         FDCE                                         r  U_Timer/counter1_reg[16]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X57Y49         FDCE (Prop_fdce_C_Q)         0.141     0.608 r  U_Timer/counter1_reg[16]/Q
                         net (fo=3, routed)           0.168     0.777    U_Timer/counter1_reg[16]
    SLICE_X57Y49         LUT2 (Prop_lut2_I0_O)        0.045     0.822 r  U_Timer/counter1[16]_i_5/O
                         net (fo=1, routed)           0.000     0.822    U_Timer/counter1[16]_i_5_n_0
    SLICE_X57Y49         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.152     0.974 r  U_Timer/counter1_reg[16]_i_1/CO[3]
                         net (fo=1, routed)           0.001     0.974    U_Timer/counter1_reg[16]_i_1_n_0
    SLICE_X57Y50         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.065     1.039 r  U_Timer/counter1_reg[20]_i_1/O[2]
                         net (fo=1, routed)           0.000     1.039    U_Timer/counter1_reg[20]_i_1_n_5
    SLICE_X57Y50         FDCE                                         r  U_Timer/counter1_reg[22]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.028    -0.119    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.063 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.237     0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.203 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.835     1.039    U_Timer/cpu_clk_BUFG
    SLICE_X57Y50         FDCE                                         r  U_Timer/counter1_reg[22]/C
                         clock pessimism             -0.305     0.734    
    SLICE_X57Y50         FDCE (Hold_fdce_C_D)         0.105     0.839    U_Timer/counter1_reg[22]
  -------------------------------------------------------------------
                         required time                         -0.839    
                         arrival time                           1.039    
  -------------------------------------------------------------------
                         slack                                  0.200    

Slack (MET) :             0.225ns  (arrival time - required time)
  Source:                 U_Timer/counter1_reg[16]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Timer/counter1_reg[21]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.597ns  (logic 0.428ns (71.692%)  route 0.169ns (28.308%))
  Logic Levels:           3  (CARRY4=2 LUT2=1)
  Clock Path Skew:        0.267ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.039ns
    Source Clock Delay      (SCD):    0.467ns
    Clock Pessimism Removal (CPR):    0.305ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.723    -0.386    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.341 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.213    -0.127    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.101 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.569     0.467    U_Timer/cpu_clk_BUFG
    SLICE_X57Y49         FDCE                                         r  U_Timer/counter1_reg[16]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X57Y49         FDCE (Prop_fdce_C_Q)         0.141     0.608 r  U_Timer/counter1_reg[16]/Q
                         net (fo=3, routed)           0.168     0.777    U_Timer/counter1_reg[16]
    SLICE_X57Y49         LUT2 (Prop_lut2_I0_O)        0.045     0.822 r  U_Timer/counter1[16]_i_5/O
                         net (fo=1, routed)           0.000     0.822    U_Timer/counter1[16]_i_5_n_0
    SLICE_X57Y49         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.152     0.974 r  U_Timer/counter1_reg[16]_i_1/CO[3]
                         net (fo=1, routed)           0.001     0.974    U_Timer/counter1_reg[16]_i_1_n_0
    SLICE_X57Y50         CARRY4 (Prop_carry4_CI_O[1])
                                                      0.090     1.064 r  U_Timer/counter1_reg[20]_i_1/O[1]
                         net (fo=1, routed)           0.000     1.064    U_Timer/counter1_reg[20]_i_1_n_6
    SLICE_X57Y50         FDCE                                         r  U_Timer/counter1_reg[21]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.028    -0.119    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.063 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.237     0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.203 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.835     1.039    U_Timer/cpu_clk_BUFG
    SLICE_X57Y50         FDCE                                         r  U_Timer/counter1_reg[21]/C
                         clock pessimism             -0.305     0.734    
    SLICE_X57Y50         FDCE (Hold_fdce_C_D)         0.105     0.839    U_Timer/counter1_reg[21]
  -------------------------------------------------------------------
                         required time                         -0.839    
                         arrival time                           1.064    
  -------------------------------------------------------------------
                         slack                                  0.225    

Slack (MET) :             0.225ns  (arrival time - required time)
  Source:                 U_Timer/counter1_reg[16]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Timer/counter1_reg[23]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.597ns  (logic 0.428ns (71.692%)  route 0.169ns (28.308%))
  Logic Levels:           3  (CARRY4=2 LUT2=1)
  Clock Path Skew:        0.267ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.039ns
    Source Clock Delay      (SCD):    0.467ns
    Clock Pessimism Removal (CPR):    0.305ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.723    -0.386    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.341 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.213    -0.127    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.101 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.569     0.467    U_Timer/cpu_clk_BUFG
    SLICE_X57Y49         FDCE                                         r  U_Timer/counter1_reg[16]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X57Y49         FDCE (Prop_fdce_C_Q)         0.141     0.608 r  U_Timer/counter1_reg[16]/Q
                         net (fo=3, routed)           0.168     0.777    U_Timer/counter1_reg[16]
    SLICE_X57Y49         LUT2 (Prop_lut2_I0_O)        0.045     0.822 r  U_Timer/counter1[16]_i_5/O
                         net (fo=1, routed)           0.000     0.822    U_Timer/counter1[16]_i_5_n_0
    SLICE_X57Y49         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.152     0.974 r  U_Timer/counter1_reg[16]_i_1/CO[3]
                         net (fo=1, routed)           0.001     0.974    U_Timer/counter1_reg[16]_i_1_n_0
    SLICE_X57Y50         CARRY4 (Prop_carry4_CI_O[3])
                                                      0.090     1.064 r  U_Timer/counter1_reg[20]_i_1/O[3]
                         net (fo=1, routed)           0.000     1.064    U_Timer/counter1_reg[20]_i_1_n_4
    SLICE_X57Y50         FDCE                                         r  U_Timer/counter1_reg[23]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.028    -0.119    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.063 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.237     0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.203 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.835     1.039    U_Timer/cpu_clk_BUFG
    SLICE_X57Y50         FDCE                                         r  U_Timer/counter1_reg[23]/C
                         clock pessimism             -0.305     0.734    
    SLICE_X57Y50         FDCE (Hold_fdce_C_D)         0.105     0.839    U_Timer/counter1_reg[23]
  -------------------------------------------------------------------
                         required time                         -0.839    
                         arrival time                           1.064    
  -------------------------------------------------------------------
                         slack                                  0.225    

Slack (MET) :             0.228ns  (arrival time - required time)
  Source:                 U_Timer/counter1_reg[16]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Timer/counter1_reg[24]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.600ns  (logic 0.431ns (71.834%)  route 0.169ns (28.166%))
  Logic Levels:           4  (CARRY4=3 LUT2=1)
  Clock Path Skew:        0.267ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.039ns
    Source Clock Delay      (SCD):    0.467ns
    Clock Pessimism Removal (CPR):    0.305ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.723    -0.386    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.341 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.213    -0.127    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.101 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.569     0.467    U_Timer/cpu_clk_BUFG
    SLICE_X57Y49         FDCE                                         r  U_Timer/counter1_reg[16]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X57Y49         FDCE (Prop_fdce_C_Q)         0.141     0.608 r  U_Timer/counter1_reg[16]/Q
                         net (fo=3, routed)           0.168     0.777    U_Timer/counter1_reg[16]
    SLICE_X57Y49         LUT2 (Prop_lut2_I0_O)        0.045     0.822 r  U_Timer/counter1[16]_i_5/O
                         net (fo=1, routed)           0.000     0.822    U_Timer/counter1[16]_i_5_n_0
    SLICE_X57Y49         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.152     0.974 r  U_Timer/counter1_reg[16]_i_1/CO[3]
                         net (fo=1, routed)           0.001     0.974    U_Timer/counter1_reg[16]_i_1_n_0
    SLICE_X57Y50         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.039     1.013 r  U_Timer/counter1_reg[20]_i_1/CO[3]
                         net (fo=1, routed)           0.000     1.013    U_Timer/counter1_reg[20]_i_1_n_0
    SLICE_X57Y51         CARRY4 (Prop_carry4_CI_O[0])
                                                      0.054     1.067 r  U_Timer/counter1_reg[24]_i_1/O[0]
                         net (fo=1, routed)           0.000     1.067    U_Timer/counter1_reg[24]_i_1_n_7
    SLICE_X57Y51         FDCE                                         r  U_Timer/counter1_reg[24]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.028    -0.119    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.063 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.237     0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.203 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.835     1.039    U_Timer/cpu_clk_BUFG
    SLICE_X57Y51         FDCE                                         r  U_Timer/counter1_reg[24]/C
                         clock pessimism             -0.305     0.734    
    SLICE_X57Y51         FDCE (Hold_fdce_C_D)         0.105     0.839    U_Timer/counter1_reg[24]
  -------------------------------------------------------------------
                         required time                         -0.839    
                         arrival time                           1.067    
  -------------------------------------------------------------------
                         slack                                  0.228    

Slack (MET) :             0.238ns  (arrival time - required time)
  Source:                 U_Button/debounce_gen[4].button_stable_reg[4]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[4].debounce_cnt_reg[4][5]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.359ns  (logic 0.189ns (52.601%)  route 0.170ns (47.399%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.014ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.025ns
    Source Clock Delay      (SCD):    0.452ns
    Clock Pessimism Removal (CPR):    0.559ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.723    -0.386    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.341 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.213    -0.127    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.101 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.554     0.452    U_Button/cpu_clk_BUFG
    SLICE_X45Y27         FDCE                                         r  U_Button/debounce_gen[4].button_stable_reg[4]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X45Y27         FDCE (Prop_fdce_C_Q)         0.141     0.593 r  U_Button/debounce_gen[4].button_stable_reg[4]/Q
                         net (fo=22, routed)          0.170     0.764    U_Button/data0[4]
    SLICE_X45Y28         LUT4 (Prop_lut4_I1_O)        0.048     0.812 r  U_Button/debounce_gen[4].debounce_cnt[4][5]_i_1/O
                         net (fo=1, routed)           0.000     0.812    U_Button/debounce_gen[4].debounce_cnt[4][5]_i_1_n_0
    SLICE_X45Y28         FDCE                                         r  U_Button/debounce_gen[4].debounce_cnt_reg[4][5]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.028    -0.119    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.063 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.237     0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.203 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.822     1.025    U_Button/cpu_clk_BUFG
    SLICE_X45Y28         FDCE                                         r  U_Button/debounce_gen[4].debounce_cnt_reg[4][5]/C
                         clock pessimism             -0.559     0.466    
    SLICE_X45Y28         FDCE (Hold_fdce_C_D)         0.107     0.573    U_Button/debounce_gen[4].debounce_cnt_reg[4][5]
  -------------------------------------------------------------------
                         required time                         -0.573    
                         arrival time                           0.812    
  -------------------------------------------------------------------
                         slack                                  0.238    

Slack (MET) :             0.239ns  (arrival time - required time)
  Source:                 U_Timer/counter1_reg[16]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Timer/counter1_reg[26]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.611ns  (logic 0.442ns (72.341%)  route 0.169ns (27.659%))
  Logic Levels:           4  (CARRY4=3 LUT2=1)
  Clock Path Skew:        0.267ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.039ns
    Source Clock Delay      (SCD):    0.467ns
    Clock Pessimism Removal (CPR):    0.305ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.723    -0.386    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.341 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.213    -0.127    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.101 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.569     0.467    U_Timer/cpu_clk_BUFG
    SLICE_X57Y49         FDCE                                         r  U_Timer/counter1_reg[16]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X57Y49         FDCE (Prop_fdce_C_Q)         0.141     0.608 r  U_Timer/counter1_reg[16]/Q
                         net (fo=3, routed)           0.168     0.777    U_Timer/counter1_reg[16]
    SLICE_X57Y49         LUT2 (Prop_lut2_I0_O)        0.045     0.822 r  U_Timer/counter1[16]_i_5/O
                         net (fo=1, routed)           0.000     0.822    U_Timer/counter1[16]_i_5_n_0
    SLICE_X57Y49         CARRY4 (Prop_carry4_S[0]_CO[3])
                                                      0.152     0.974 r  U_Timer/counter1_reg[16]_i_1/CO[3]
                         net (fo=1, routed)           0.001     0.974    U_Timer/counter1_reg[16]_i_1_n_0
    SLICE_X57Y50         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.039     1.013 r  U_Timer/counter1_reg[20]_i_1/CO[3]
                         net (fo=1, routed)           0.000     1.013    U_Timer/counter1_reg[20]_i_1_n_0
    SLICE_X57Y51         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.065     1.078 r  U_Timer/counter1_reg[24]_i_1/O[2]
                         net (fo=1, routed)           0.000     1.078    U_Timer/counter1_reg[24]_i_1_n_5
    SLICE_X57Y51         FDCE                                         r  U_Timer/counter1_reg[26]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.028    -0.119    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.063 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.237     0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.203 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.835     1.039    U_Timer/cpu_clk_BUFG
    SLICE_X57Y51         FDCE                                         r  U_Timer/counter1_reg[26]/C
                         clock pessimism             -0.305     0.734    
    SLICE_X57Y51         FDCE (Hold_fdce_C_D)         0.105     0.839    U_Timer/counter1_reg[26]
  -------------------------------------------------------------------
                         required time                         -0.839    
                         arrival time                           1.078    
  -------------------------------------------------------------------
                         slack                                  0.239    

Slack (MET) :             0.251ns  (arrival time - required time)
  Source:                 U_Button/debounce_gen[4].button_stable_reg[4]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[4].debounce_cnt_reg[4][4]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.356ns  (logic 0.186ns (52.202%)  route 0.170ns (47.798%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.014ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.025ns
    Source Clock Delay      (SCD):    0.452ns
    Clock Pessimism Removal (CPR):    0.559ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.723    -0.386    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.341 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.213    -0.127    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.101 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.554     0.452    U_Button/cpu_clk_BUFG
    SLICE_X45Y27         FDCE                                         r  U_Button/debounce_gen[4].button_stable_reg[4]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X45Y27         FDCE (Prop_fdce_C_Q)         0.141     0.593 r  U_Button/debounce_gen[4].button_stable_reg[4]/Q
                         net (fo=22, routed)          0.170     0.764    U_Button/data0[4]
    SLICE_X45Y28         LUT4 (Prop_lut4_I1_O)        0.045     0.809 r  U_Button/debounce_gen[4].debounce_cnt[4][4]_i_1/O
                         net (fo=1, routed)           0.000     0.809    U_Button/debounce_gen[4].debounce_cnt[4][4]_i_1_n_0
    SLICE_X45Y28         FDCE                                         r  U_Button/debounce_gen[4].debounce_cnt_reg[4][4]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.028    -0.119    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.063 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.237     0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.203 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.822     1.025    U_Button/cpu_clk_BUFG
    SLICE_X45Y28         FDCE                                         r  U_Button/debounce_gen[4].debounce_cnt_reg[4][4]/C
                         clock pessimism             -0.559     0.466    
    SLICE_X45Y28         FDCE (Hold_fdce_C_D)         0.091     0.557    U_Button/debounce_gen[4].debounce_cnt_reg[4][4]
  -------------------------------------------------------------------
                         required time                         -0.557    
                         arrival time                           0.809    
  -------------------------------------------------------------------
                         slack                                  0.251    

Slack (MET) :             0.253ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][3]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.373ns  (logic 0.186ns (49.929%)  route 0.187ns (50.071%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.029ns
    Source Clock Delay      (SCD):    0.456ns
    Clock Pessimism Removal (CPR):    0.560ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.723    -0.386    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.341 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.213    -0.127    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.101 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.558     0.456    U_Button/cpu_clk_BUFG
    SLICE_X48Y30         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y30         FDCE (Prop_fdce_C_Q)         0.141     0.597 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.187     0.784    U_Button/p_1_in8_in
    SLICE_X49Y30         LUT4 (Prop_lut4_I0_O)        0.045     0.829 r  U_Button/debounce_gen[1].debounce_cnt[1][3]_i_1/O
                         net (fo=1, routed)           0.000     0.829    U_Button/debounce_gen[1].debounce_cnt[1][3]_i_1_n_0
    SLICE_X49Y30         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][3]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.028    -0.119    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.063 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.237     0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.203 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.826     1.029    U_Button/cpu_clk_BUFG
    SLICE_X49Y30         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][3]/C
                         clock pessimism             -0.560     0.469    
    SLICE_X49Y30         FDCE (Hold_fdce_C_D)         0.107     0.576    U_Button/debounce_gen[1].debounce_cnt_reg[1][3]
  -------------------------------------------------------------------
                         required time                         -0.576    
                         arrival time                           0.829    
  -------------------------------------------------------------------
                         slack                                  0.253    





Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clk_out1_cpuclk
Waveform(ns):       { 0.000 20.000 }
Period(ns):         40.000
Sources:            { Clkgen/inst/plle2_adv_inst/CLKOUT0 }

Check Type        Corner  Lib Pin            Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     BUFG/I             n/a            2.155         40.000      37.845     BUFGCTRL_X0Y2   Clkgen/inst/clkout1_buf/I
Min Period        n/a     BUFG/I             n/a            2.155         40.000      37.845     BUFGCTRL_X0Y0   cpu_clk_BUFG_inst/I
Min Period        n/a     PLLE2_ADV/CLKOUT0  n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKOUT0
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X45Y52    Core_cpu/U_PC/pc_reg[0]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X53Y54    Core_cpu/U_PC/pc_reg[10]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X48Y54    Core_cpu/U_PC/pc_reg[11]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X45Y54    Core_cpu/U_PC/pc_reg[12]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X48Y55    Core_cpu/U_PC/pc_reg[13]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X45Y55    Core_cpu/U_PC/pc_reg[14]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X45Y55    Core_cpu/U_PC/pc_reg[15]/C
Max Period        n/a     PLLE2_ADV/CLKOUT0  n/a            160.000       40.000      120.000    PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKOUT0
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X46Y131   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12288_12543_22_22/RAMS64E_A/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X46Y131   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12288_12543_22_22/RAMS64E_B/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X46Y131   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12288_12543_22_22/RAMS64E_C/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X46Y131   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12288_12543_22_22/RAMS64E_D/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X10Y14    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_3_3/RAMS64E_B/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X10Y14    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_3_3/RAMS64E_C/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X10Y14    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_3_3/RAMS64E_D/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y47    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_4_4/RAMS64E_A/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y47    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_4_4/RAMS64E_B/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y47    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_4_4/RAMS64E_C/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X14Y71    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_31_31/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X14Y71    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_31_31/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X14Y71    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_31_31/RAMS64E_C/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X42Y34    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_12_12/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X42Y34    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_12_12/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X42Y34    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_12_12/RAMS64E_C/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X42Y34    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_12_12/RAMS64E_D/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X52Y113   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12288_12543_23_23/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X10Y14    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_3_3/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X10Y14    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_3_3/RAMS64E_C/CLK



---------------------------------------------------------------------------------------------------
From Clock:  clkfbout_cpuclk
  To Clock:  clkfbout_cpuclk

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack       12.633ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clkfbout_cpuclk
Waveform(ns):       { 0.000 20.000 }
Period(ns):         40.000
Sources:            { Clkgen/inst/plle2_adv_inst/CLKFBOUT }

Check Type  Corner  Lib Pin             Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period  n/a     BUFG/I              n/a            2.155         40.000      37.845     BUFGCTRL_X0Y1   Clkgen/inst/clkf_buf/I
Min Period  n/a     PLLE2_ADV/CLKFBOUT  n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBOUT
Min Period  n/a     PLLE2_ADV/CLKFBIN   n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBIN
Max Period  n/a     PLLE2_ADV/CLKFBIN   n/a            52.633        40.000      12.633     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBIN
Max Period  n/a     PLLE2_ADV/CLKFBOUT  n/a            160.000       40.000      120.000    PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBOUT



