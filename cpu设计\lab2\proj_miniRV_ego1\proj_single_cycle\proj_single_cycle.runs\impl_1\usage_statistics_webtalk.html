<HTML><HEAD><TITLE>Device Usage Statistics Report</TITLE></HEAD>
<BODY TEXT='#000000' BGCOLOR='#FFFFFF' LINK='#0000EE' VLINK='#551A8B' ALINK='#FF0000'><H3>Device Usage Page (usage_statistics_webtalk.html)</H3>This HTML page displays the device usage statistics that will be sent to Xilinx.<BR>To see the actual file transmitted to Xilinx, please click <A HREF="./usage_statistics_webtalk.xml">here</A>.<BR><BR><HR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>software_version_and_target_device</B></TD></TR>
<TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>beta</B></TD><TD>FALSE</TD>
  <TD BGCOLOR='#DBE5F1'><B>build_version</B></TD><TD>2405991</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>date_generated</B></TD><TD>Thu Jul  3 17:15:08 2025</TD>
  <TD BGCOLOR='#DBE5F1'><B>os_platform</B></TD><TD>WIN64</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>product_version</B></TD><TD>Vivado v2018.3 (64-bit)</TD>
  <TD BGCOLOR='#DBE5F1'><B>project_id</B></TD><TD>c7409b1ddabf4fa18ec9204b6edbe050</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>project_iteration</B></TD><TD>1</TD>
  <TD BGCOLOR='#DBE5F1'><B>random_id</B></TD><TD>2db4f8578c585290a1c83d0c55d08d2e</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>registration_id</B></TD><TD>2db4f8578c585290a1c83d0c55d08d2e</TD>
  <TD BGCOLOR='#DBE5F1'><B>route_design</B></TD><TD>TRUE</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>target_device</B></TD><TD>xc7a35t</TD>
  <TD BGCOLOR='#DBE5F1'><B>target_family</B></TD><TD>artix7</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>target_package</B></TD><TD>csg324</TD>
  <TD BGCOLOR='#DBE5F1'><B>target_speed</B></TD><TD>-1</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>tool_flow</B></TD><TD>Vivado</TD>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>user_environment</B></TD></TR>
<TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>cpu_name</B></TD><TD>13th Gen Intel(R) Core(TM) i9-13980HX</TD>
  <TD BGCOLOR='#DBE5F1'><B>cpu_speed</B></TD><TD>2419 MHz</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>os_name</B></TD><TD>Microsoft Windows 8 or later , 64-bit</TD>
  <TD BGCOLOR='#DBE5F1'><B>os_release</B></TD><TD>major release  (build 9200)</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>system_ram</B></TD><TD>33.000 GB</TD>
  <TD BGCOLOR='#DBE5F1'><B>total_processors</B></TD><TD>1</TD>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>vivado_usage</B></TD></TR>
<TR ALIGN='LEFT'>  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>gui_handlers</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>addsrcwizard_specify_hdl_netlist_block_design=1</TD>
   <TD>addsrcwizard_specify_simulation_specific_hdl_files=1</TD>
   <TD>basedialog_apply=1</TD>
   <TD>basedialog_cancel=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>basedialog_ok=35</TD>
   <TD>coretreetablepanel_core_tree_table=21</TD>
   <TD>createsrcfiledialog_file_name=18</TD>
   <TD>customizeerrordialog_messages=4</TD>
</TR><TR ALIGN='LEFT'>   <TD>customizeerrordialog_ok=2</TD>
   <TD>definemodulesdialog_new_source_files=5</TD>
   <TD>filesetpanel_file_set_panel_tree=180</TD>
   <TD>flownavigatortreepanel_flow_navigator_tree=8</TD>
</TR><TR ALIGN='LEFT'>   <TD>hjfilechooserhelpers_jump_to_recent_project_directory=3</TD>
   <TD>ipcoreview_tabbed_pane=2</TD>
   <TD>mainmenumgr_tools=2</TD>
   <TD>numjobschooser_number_of_jobs=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_auto_update_hier=3</TD>
   <TD>pacommandnames_run_bitgen=1</TD>
   <TD>paviews_code=1</TD>
   <TD>paviews_ip_catalog=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>paviews_project_summary=1</TD>
   <TD>rdicommands_settings=1</TD>
   <TD>settingsdialog_options_tree=1</TD>
   <TD>settingseditorpage_enter_command_line_for_custom=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>settingseditorpage_use_this_drop_down_list_box_to_select=1</TD>
   <TD>simpleoutputproductdialog_generate_output_products_immediately=4</TD>
   <TD>srcchooserpanel_create_file=17</TD>
   <TD>srcmenu_ip_hierarchy=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>xpg_coefilewidgdet_browse=3</TD>
</TR>  </TABLE>
  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>java_command_handlers</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>addsources=4</TD>
   <TD>coreview=3</TD>
   <TD>customizecore=4</TD>
   <TD>recustomizecore=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>runbitgen=1</TD>
   <TD>runimplementation=1</TD>
   <TD>runsynthesis=1</TD>
   <TD>toolssettings=1</TD>
</TR>  </TABLE>
</TR><TR ALIGN='LEFT'>  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>other_data</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>guimode=2</TD>
</TR>  </TABLE>
  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>project_data</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>constraintsetcount=2</TD>
   <TD>core_container=false</TD>
   <TD>currentimplrun=impl_1</TD>
   <TD>currentsynthesisrun=synth_1</TD>
</TR><TR ALIGN='LEFT'>   <TD>default_library=xil_defaultlib</TD>
   <TD>designmode=RTL</TD>
   <TD>export_simulation_activehdl=13</TD>
   <TD>export_simulation_ies=13</TD>
</TR><TR ALIGN='LEFT'>   <TD>export_simulation_modelsim=13</TD>
   <TD>export_simulation_questa=13</TD>
   <TD>export_simulation_riviera=13</TD>
   <TD>export_simulation_vcs=13</TD>
</TR><TR ALIGN='LEFT'>   <TD>export_simulation_xsim=13</TD>
   <TD>implstrategy=Vivado Implementation Defaults</TD>
   <TD>launch_simulation_activehdl=0</TD>
   <TD>launch_simulation_ies=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>launch_simulation_modelsim=0</TD>
   <TD>launch_simulation_questa=0</TD>
   <TD>launch_simulation_riviera=0</TD>
   <TD>launch_simulation_vcs=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>launch_simulation_xsim=39</TD>
   <TD>simulator_language=Mixed</TD>
   <TD>srcsetcount=16</TD>
   <TD>synthesisstrategy=Vivado Synthesis Defaults</TD>
</TR><TR ALIGN='LEFT'>   <TD>target_language=Verilog</TD>
   <TD>target_simulator=XSim</TD>
   <TD>totalimplruns=4</TD>
   <TD>totalsynthesisruns=4</TD>
</TR>  </TABLE>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>unisim_transformation</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>post_unisim_transformation</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufg=3</TD>
    <TD>carry4=105</TD>
    <TD>fdce=298</TD>
    <TD>fdpe=12</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdre=32</TD>
    <TD>gnd=14</TD>
    <TD>ibuf=23</TD>
    <TD>lut1=37</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut2=102</TD>
    <TD>lut3=104</TD>
    <TD>lut4=248</TD>
    <TD>lut5=250</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut6=1274</TD>
    <TD>muxf7=4357</TD>
    <TD>muxf8=2176</TD>
    <TD>obuf=40</TD>
</TR><TR ALIGN='LEFT'>    <TD>plle2_adv=1</TD>
    <TD>ramd32=72</TD>
    <TD>rams32=24</TD>
    <TD>rams64e=8192</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcc=9</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>pre_unisim_transformation</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufg=3</TD>
    <TD>carry4=105</TD>
    <TD>fdce=298</TD>
    <TD>fdpe=12</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdre=32</TD>
    <TD>gnd=14</TD>
    <TD>ibuf=23</TD>
    <TD>lut1=37</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut2=102</TD>
    <TD>lut3=104</TD>
    <TD>lut4=248</TD>
    <TD>lut5=250</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut6=1274</TD>
    <TD>muxf7=261</TD>
    <TD>muxf8=128</TD>
    <TD>obuf=40</TD>
</TR><TR ALIGN='LEFT'>    <TD>plle2_adv=1</TD>
    <TD>ram256x1s=2048</TD>
    <TD>ram32m=12</TD>
    <TD>vcc=9</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>ip_statistics</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>clk_wiz_v6_0_2_0_0/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>clkin1_period=10.000</TD>
    <TD>clkin2_period=10.000</TD>
    <TD>clock_mgr_type=NA</TD>
    <TD>component_name=cpuclk</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>enable_axi=0</TD>
    <TD>feedback_source=FDBK_AUTO</TD>
    <TD>feedback_type=SINGLE</TD>
</TR><TR ALIGN='LEFT'>    <TD>iptotal=1</TD>
    <TD>manual_override=false</TD>
    <TD>num_out_clk=1</TD>
    <TD>primitive=PLL</TD>
</TR><TR ALIGN='LEFT'>    <TD>use_dyn_phase_shift=false</TD>
    <TD>use_dyn_reconfig=false</TD>
    <TD>use_inclk_stopped=false</TD>
    <TD>use_inclk_switchover=false</TD>
</TR><TR ALIGN='LEFT'>    <TD>use_locked=true</TD>
    <TD>use_max_i_jitter=false</TD>
    <TD>use_min_o_jitter=false</TD>
    <TD>use_phase_alignment=true</TD>
</TR><TR ALIGN='LEFT'>    <TD>use_power_down=false</TD>
    <TD>use_reset=false</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>dist_mem_gen_v8_0_12/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_addr_width=14</TD>
    <TD>c_default_data=0</TD>
    <TD>c_depth=16384</TD>
    <TD>c_elaboration_dir=./</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_family=artix7</TD>
    <TD>c_has_clk=1</TD>
    <TD>c_has_d=1</TD>
    <TD>c_has_dpo=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_has_dpra=0</TD>
    <TD>c_has_i_ce=0</TD>
    <TD>c_has_qdpo=0</TD>
    <TD>c_has_qdpo_ce=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_has_qdpo_clk=0</TD>
    <TD>c_has_qdpo_rst=0</TD>
    <TD>c_has_qdpo_srst=0</TD>
    <TD>c_has_qspo=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_has_qspo_ce=0</TD>
    <TD>c_has_qspo_rst=0</TD>
    <TD>c_has_qspo_srst=0</TD>
    <TD>c_has_spo=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_has_we=1</TD>
    <TD>c_mem_init_file=no_coe_file_loaded</TD>
    <TD>c_mem_type=1</TD>
    <TD>c_parser_type=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_pipeline_stages=0</TD>
    <TD>c_qce_joined=0</TD>
    <TD>c_qualify_we=0</TD>
    <TD>c_read_mif=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_reg_a_d_inputs=0</TD>
    <TD>c_reg_dpra_input=0</TD>
    <TD>c_sync_enable=1</TD>
    <TD>c_width=32</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=false</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=12</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=dist_mem_gen</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=8.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>dist_mem_gen_v8_0_12/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_addr_width=14</TD>
    <TD>c_default_data=0</TD>
    <TD>c_depth=16384</TD>
    <TD>c_elaboration_dir=./</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_family=artix7</TD>
    <TD>c_has_clk=0</TD>
    <TD>c_has_d=0</TD>
    <TD>c_has_dpo=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_has_dpra=0</TD>
    <TD>c_has_i_ce=0</TD>
    <TD>c_has_qdpo=0</TD>
    <TD>c_has_qdpo_ce=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_has_qdpo_clk=0</TD>
    <TD>c_has_qdpo_rst=0</TD>
    <TD>c_has_qdpo_srst=0</TD>
    <TD>c_has_qspo=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_has_qspo_ce=0</TD>
    <TD>c_has_qspo_rst=0</TD>
    <TD>c_has_qspo_srst=0</TD>
    <TD>c_has_spo=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_has_we=0</TD>
    <TD>c_mem_init_file=[user-defined]</TD>
    <TD>c_mem_type=0</TD>
    <TD>c_parser_type=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_pipeline_stages=0</TD>
    <TD>c_qce_joined=0</TD>
    <TD>c_qualify_we=0</TD>
    <TD>c_read_mif=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_reg_a_d_inputs=0</TD>
    <TD>c_reg_dpra_input=0</TD>
    <TD>c_sync_enable=1</TD>
    <TD>c_width=32</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=false</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=12</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=dist_mem_gen</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=8.0</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_drc</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-append=default::[not_specified]</TD>
    <TD>-checks=default::[not_specified]</TD>
    <TD>-fail_on=default::[not_specified]</TD>
    <TD>-force=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-format=default::[not_specified]</TD>
    <TD>-internal=default::[not_specified]</TD>
    <TD>-internal_only=default::[not_specified]</TD>
    <TD>-messages=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-name=default::[not_specified]</TD>
    <TD>-no_waivers=default::[not_specified]</TD>
    <TD>-return_string=default::[not_specified]</TD>
    <TD>-ruledecks=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-upgrade_cw=default::[not_specified]</TD>
    <TD>-waived=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>results</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>cfgbvs-1=1</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_utilization</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>clocking</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufgctrl_available=32</TD>
    <TD>bufgctrl_fixed=0</TD>
    <TD>bufgctrl_used=3</TD>
    <TD>bufgctrl_util_percentage=9.38</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufhce_available=72</TD>
    <TD>bufhce_fixed=0</TD>
    <TD>bufhce_used=0</TD>
    <TD>bufhce_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufio_available=20</TD>
    <TD>bufio_fixed=0</TD>
    <TD>bufio_used=0</TD>
    <TD>bufio_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufmrce_available=10</TD>
    <TD>bufmrce_fixed=0</TD>
    <TD>bufmrce_used=0</TD>
    <TD>bufmrce_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufr_available=20</TD>
    <TD>bufr_fixed=0</TD>
    <TD>bufr_used=0</TD>
    <TD>bufr_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>mmcme2_adv_available=5</TD>
    <TD>mmcme2_adv_fixed=0</TD>
    <TD>mmcme2_adv_used=0</TD>
    <TD>mmcme2_adv_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>plle2_adv_available=5</TD>
    <TD>plle2_adv_fixed=0</TD>
    <TD>plle2_adv_used=1</TD>
    <TD>plle2_adv_util_percentage=20.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>dsp</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>dsps_available=90</TD>
    <TD>dsps_fixed=0</TD>
    <TD>dsps_used=0</TD>
    <TD>dsps_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>io_standard</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>blvds_25=0</TD>
    <TD>diff_hstl_i=0</TD>
    <TD>diff_hstl_i_18=0</TD>
    <TD>diff_hstl_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_hstl_ii_18=0</TD>
    <TD>diff_hsul_12=0</TD>
    <TD>diff_mobile_ddr=0</TD>
    <TD>diff_sstl135=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl135_r=0</TD>
    <TD>diff_sstl15=0</TD>
    <TD>diff_sstl15_r=0</TD>
    <TD>diff_sstl18_i=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl18_ii=0</TD>
    <TD>hstl_i=0</TD>
    <TD>hstl_i_18=0</TD>
    <TD>hstl_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>hstl_ii_18=0</TD>
    <TD>hsul_12=0</TD>
    <TD>lvcmos12=0</TD>
    <TD>lvcmos15=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvcmos18=0</TD>
    <TD>lvcmos25=0</TD>
    <TD>lvcmos33=1</TD>
    <TD>lvds_25=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvttl=0</TD>
    <TD>mini_lvds_25=0</TD>
    <TD>mobile_ddr=0</TD>
    <TD>pci33_3=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>ppds_25=0</TD>
    <TD>rsds_25=0</TD>
    <TD>sstl135=0</TD>
    <TD>sstl135_r=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>sstl15=0</TD>
    <TD>sstl15_r=0</TD>
    <TD>sstl18_i=0</TD>
    <TD>sstl18_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>tmds_33=0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>memory</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>block_ram_tile_available=50</TD>
    <TD>block_ram_tile_fixed=0</TD>
    <TD>block_ram_tile_used=0</TD>
    <TD>block_ram_tile_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb18_available=100</TD>
    <TD>ramb18_fixed=0</TD>
    <TD>ramb18_used=0</TD>
    <TD>ramb18_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb36_fifo_available=50</TD>
    <TD>ramb36_fifo_fixed=0</TD>
    <TD>ramb36_fifo_used=0</TD>
    <TD>ramb36_fifo_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>primitives</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufg_functional_category=Clock</TD>
    <TD>bufg_used=3</TD>
    <TD>carry4_functional_category=CarryLogic</TD>
    <TD>carry4_used=105</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdce_functional_category=Flop &amp; Latch</TD>
    <TD>fdce_used=298</TD>
    <TD>fdpe_functional_category=Flop &amp; Latch</TD>
    <TD>fdpe_used=12</TD>
</TR><TR ALIGN='LEFT'>    <TD>ibuf_functional_category=IO</TD>
    <TD>ibuf_used=23</TD>
    <TD>lut1_functional_category=LUT</TD>
    <TD>lut1_used=36</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut2_functional_category=LUT</TD>
    <TD>lut2_used=105</TD>
    <TD>lut3_functional_category=LUT</TD>
    <TD>lut3_used=105</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut4_functional_category=LUT</TD>
    <TD>lut4_used=243</TD>
    <TD>lut5_functional_category=LUT</TD>
    <TD>lut5_used=252</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut6_functional_category=LUT</TD>
    <TD>lut6_used=1272</TD>
    <TD>muxf7_functional_category=MuxFx</TD>
    <TD>muxf7_used=4357</TD>
</TR><TR ALIGN='LEFT'>    <TD>muxf8_functional_category=MuxFx</TD>
    <TD>muxf8_used=2176</TD>
    <TD>obuf_functional_category=IO</TD>
    <TD>obuf_used=40</TD>
</TR><TR ALIGN='LEFT'>    <TD>plle2_adv_functional_category=Clock</TD>
    <TD>plle2_adv_used=1</TD>
    <TD>ramd32_functional_category=Distributed Memory</TD>
    <TD>ramd32_used=72</TD>
</TR><TR ALIGN='LEFT'>    <TD>rams32_functional_category=Distributed Memory</TD>
    <TD>rams32_used=24</TD>
    <TD>rams64e_functional_category=Distributed Memory</TD>
    <TD>rams64e_used=8192</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>slice_logic</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>f7_muxes_available=16300</TD>
    <TD>f7_muxes_fixed=0</TD>
    <TD>f7_muxes_used=4357</TD>
    <TD>f7_muxes_util_percentage=26.73</TD>
</TR><TR ALIGN='LEFT'>    <TD>f8_muxes_available=8150</TD>
    <TD>f8_muxes_fixed=0</TD>
    <TD>f8_muxes_used=2176</TD>
    <TD>f8_muxes_util_percentage=26.70</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_distributed_ram_fixed=0</TD>
    <TD>lut_as_distributed_ram_used=8240</TD>
    <TD>lut_as_logic_available=20800</TD>
    <TD>lut_as_logic_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_logic_used=1835</TD>
    <TD>lut_as_logic_util_percentage=8.82</TD>
    <TD>lut_as_memory_available=9600</TD>
    <TD>lut_as_memory_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_memory_used=8240</TD>
    <TD>lut_as_memory_util_percentage=85.83</TD>
    <TD>lut_as_shift_register_fixed=0</TD>
    <TD>lut_as_shift_register_used=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_as_flip_flop_available=41600</TD>
    <TD>register_as_flip_flop_fixed=0</TD>
    <TD>register_as_flip_flop_used=310</TD>
    <TD>register_as_flip_flop_util_percentage=0.75</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_as_latch_available=41600</TD>
    <TD>register_as_latch_fixed=0</TD>
    <TD>register_as_latch_used=0</TD>
    <TD>register_as_latch_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_luts_available=20800</TD>
    <TD>slice_luts_fixed=0</TD>
    <TD>slice_luts_used=10075</TD>
    <TD>slice_luts_util_percentage=48.44</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_available=41600</TD>
    <TD>slice_registers_fixed=0</TD>
    <TD>slice_registers_used=310</TD>
    <TD>slice_registers_util_percentage=0.75</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_distributed_ram_fixed=0</TD>
    <TD>lut_as_distributed_ram_used=8240</TD>
    <TD>lut_as_logic_available=20800</TD>
    <TD>lut_as_logic_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_logic_used=1835</TD>
    <TD>lut_as_logic_util_percentage=8.82</TD>
    <TD>lut_as_memory_available=9600</TD>
    <TD>lut_as_memory_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_memory_used=8240</TD>
    <TD>lut_as_memory_util_percentage=85.83</TD>
    <TD>lut_as_shift_register_fixed=0</TD>
    <TD>lut_as_shift_register_used=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_in_front_of_the_register_is_unused_fixed=0</TD>
    <TD>lut_in_front_of_the_register_is_unused_used=68</TD>
    <TD>lut_in_front_of_the_register_is_used_fixed=68</TD>
    <TD>lut_in_front_of_the_register_is_used_used=20</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_driven_from_outside_the_slice_fixed=20</TD>
    <TD>register_driven_from_outside_the_slice_used=88</TD>
    <TD>register_driven_from_within_the_slice_fixed=88</TD>
    <TD>register_driven_from_within_the_slice_used=222</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_available=8150</TD>
    <TD>slice_fixed=0</TD>
    <TD>slice_registers_available=41600</TD>
    <TD>slice_registers_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_used=310</TD>
    <TD>slice_registers_util_percentage=0.75</TD>
    <TD>slice_used=2672</TD>
    <TD>slice_util_percentage=32.79</TD>
</TR><TR ALIGN='LEFT'>    <TD>slicel_fixed=0</TD>
    <TD>slicel_used=577</TD>
    <TD>slicem_fixed=0</TD>
    <TD>slicem_used=2095</TD>
</TR><TR ALIGN='LEFT'>    <TD>unique_control_sets_available=8150</TD>
    <TD>unique_control_sets_fixed=8150</TD>
    <TD>unique_control_sets_used=74</TD>
    <TD>unique_control_sets_util_percentage=0.91</TD>
</TR><TR ALIGN='LEFT'>    <TD>using_o5_and_o6_fixed=0.91</TD>
    <TD>using_o5_and_o6_used=48</TD>
    <TD>using_o5_output_only_fixed=48</TD>
    <TD>using_o5_output_only_used=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>using_o6_output_only_fixed=0</TD>
    <TD>using_o6_output_only_used=8192</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>specific_feature</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bscane2_available=4</TD>
    <TD>bscane2_fixed=0</TD>
    <TD>bscane2_used=0</TD>
    <TD>bscane2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>capturee2_available=1</TD>
    <TD>capturee2_fixed=0</TD>
    <TD>capturee2_used=0</TD>
    <TD>capturee2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>dna_port_available=1</TD>
    <TD>dna_port_fixed=0</TD>
    <TD>dna_port_used=0</TD>
    <TD>dna_port_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>efuse_usr_available=1</TD>
    <TD>efuse_usr_fixed=0</TD>
    <TD>efuse_usr_used=0</TD>
    <TD>efuse_usr_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>frame_ecce2_available=1</TD>
    <TD>frame_ecce2_fixed=0</TD>
    <TD>frame_ecce2_used=0</TD>
    <TD>frame_ecce2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>icape2_available=2</TD>
    <TD>icape2_fixed=0</TD>
    <TD>icape2_used=0</TD>
    <TD>icape2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcie_2_1_available=1</TD>
    <TD>pcie_2_1_fixed=0</TD>
    <TD>pcie_2_1_used=0</TD>
    <TD>pcie_2_1_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>startupe2_available=1</TD>
    <TD>startupe2_fixed=0</TD>
    <TD>startupe2_used=0</TD>
    <TD>startupe2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>xadc_available=1</TD>
    <TD>xadc_fixed=0</TD>
    <TD>xadc_used=0</TD>
    <TD>xadc_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>synthesis</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-assert=default::[not_specified]</TD>
    <TD>-bufg=default::12</TD>
    <TD>-cascade_dsp=default::auto</TD>
    <TD>-constrset=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-control_set_opt_threshold=default::auto</TD>
    <TD>-directive=default::default</TD>
    <TD>-fanout_limit=default::10000</TD>
    <TD>-flatten_hierarchy=default::rebuilt</TD>
</TR><TR ALIGN='LEFT'>    <TD>-fsm_extraction=default::auto</TD>
    <TD>-gated_clock_conversion=default::off</TD>
    <TD>-generic=default::[not_specified]</TD>
    <TD>-include_dirs=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-keep_equivalent_registers=default::[not_specified]</TD>
    <TD>-max_bram=default::-1</TD>
    <TD>-max_bram_cascade_height=default::-1</TD>
    <TD>-max_dsp=default::-1</TD>
</TR><TR ALIGN='LEFT'>    <TD>-max_uram=default::-1</TD>
    <TD>-max_uram_cascade_height=default::-1</TD>
    <TD>-mode=default::default</TD>
    <TD>-name=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-no_lc=default::[not_specified]</TD>
    <TD>-no_srlextract=default::[not_specified]</TD>
    <TD>-no_timing_driven=default::[not_specified]</TD>
    <TD>-part=xc7a35tcsg324-1</TD>
</TR><TR ALIGN='LEFT'>    <TD>-resource_sharing=default::auto</TD>
    <TD>-retiming=default::[not_specified]</TD>
    <TD>-rtl=default::[not_specified]</TD>
    <TD>-rtl_skip_constraints=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-rtl_skip_ip=default::[not_specified]</TD>
    <TD>-seu_protect=default::none</TD>
    <TD>-sfcu=default::[not_specified]</TD>
    <TD>-shreg_min_size=default::3</TD>
</TR><TR ALIGN='LEFT'>    <TD>-top=miniRV_SoC</TD>
    <TD>-verilog_define=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>elapsed=00:00:24s</TD>
    <TD>hls_ip=0</TD>
    <TD>memory_gain=699.367MB</TD>
    <TD>memory_peak=1055.367MB</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
</BODY>
</HTML>
