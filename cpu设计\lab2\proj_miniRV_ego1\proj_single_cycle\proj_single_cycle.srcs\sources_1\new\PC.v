`timescale 1ns / 1ps

// TODO: PC模块 - 程序计数器
// 功能：存储当前指令地址，在时钟上升沿更新
module PC (
    input  wire         rst,        // 复位信号
    input  wire         clk,        // 时钟信号
    input  wire [31:0]  din,        // 下一个PC值
    output reg  [31:0]  pc          // 当前PC值
);

    // TODO: PC寄存器，在时钟上升沿更新
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            pc <= 32'h0000_0000;       // 复位时PC为0
        end else begin
            pc <= din;                 // 正常情况下更新为下一个PC值
        end
    end

endmodule