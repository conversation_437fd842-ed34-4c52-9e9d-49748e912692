`timescale 1ns / 1ps

`include "defines.vh"

module NPC (
    input  wire [31:0]  PC,         
    input  wire [31:0]  IMM,       
    input  wire [31:0]  ALU_C,    
    input  wire [1:0]   npc_op,    
    output reg  [31:0]  npc,      
    output wire [31:0]  pc4      
);

    assign pc4 = PC + 4;

    // 根据npc_op选择下一个PC值
    always @(*) begin
        case (npc_op)
            `NPC_PC4:     npc = PC + 4;          // 顺序执行：PC + 4
            `NPC_PCIMM:   npc = PC + IMM;        // 分支和jal：PC + imm
            `NPC_RD1IMM:  npc = ALU_C & ~1;      // jalr：选择ALU结果并对齐
            default:      npc = PC + 4;                  
        endcase
    end

endmodule