`timescale 1ns / 1ps

`include "defines.vh"

module Timer (
    input  wire         rst,        
    input  wire         clk,        
    input  wire [31:0]  addr,      
    input  wire         we,        
    input  wire [31:0]  wdata,     
    output reg  [31:0]  rdata      
);

    // 计时器寄存器
    reg [31:0] counter0;        // 主计数器 (0x20地址)
    reg [31:0] counter1;        // 分频计数器
    reg [31:0] threshold;       // 分频阈值 (0x24地址)

    // TODO: 计时器逻辑 - 根据实验指导书图9-6的原理
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            counter0 <= 32'h0;
            counter1 <= 32'h0;
            threshold <= 32'd25000000;  // 默认1秒分频 (25MHz)
        end else begin
            // 分频计数器递增
            if (counter1 >= threshold - 1) begin
                counter1 <= 32'h0;
                counter0 <= counter0 + 1'b1;  // 主计数器递增
            end else begin
                counter1 <= counter1 + 1'b1;
            end

            // TODO: CPU写操作处理
            if (we) begin
                case (addr)
                    `PERI_ADDR_TIM0: counter0 <= wdata;    // 写计数值
                    `PERI_ADDR_TIM1: threshold <= wdata;   // 写分频系数
                endcase
            end
        end
    end

    // TODO: CPU读操作处理
    always @(*) begin
        case (addr)
            `PERI_ADDR_TIM0: rdata = counter0;     // 读计数值
            `PERI_ADDR_TIM1: rdata = threshold;    // 读分频系数
            default:         rdata = 32'h0;
        endcase
    end

endmodule
