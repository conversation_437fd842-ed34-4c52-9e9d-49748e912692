`timescale 1ns / 1ps

`include "defines.vh"

// TODO: 拨码开关接口模块
// 功能：读取16位拨码开关状态
module Switch (
    input  wire         rst,        // 复位信号
    input  wire         clk,        // 时钟信号
    input  wire [31:0]  addr,       // 地址信号
    output reg  [31:0]  rdata,      // 读数据信号

    input  wire [15:0]  sw          // 拨码开关硬件接口
);

    // TODO: 读拨码开关状态
    always @(*) begin
        if (addr == `PERI_ADDR_SW) begin
            rdata = {16'h0, sw};  // 高16位为0，低16位为开关状态
        end else begin
            rdata = 32'h0;
        end
    end

endmodule
