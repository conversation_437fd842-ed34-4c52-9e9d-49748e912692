<?xml version="1.0" encoding="UTF-8" ?>
<document>
<!--The data in this file is primarily intended for consumption by Xilinx tools.
The structure and the elements are likely to change over the next few releases.
This means code written to parse this file will need to be revisited each subsequent release.-->
<application name="pa" timeStamp="Thu Jul  3 18:40:30 2025">
<section name="Project Information" visible="false">
<property name="ProjectID" value="af376fe724ed414d9dd6f0a900d6efa1" type="ProjectID"/>
<property name="ProjectIteration" value="5" type="ProjectIteration"/>
</section>
<section name="PlanAhead Usage" visible="true">
<item name="Project Data">
<property name="SrcSetCount" value="1" type="SrcSetCount"/>
<property name="ConstraintSetCount" value="1" type="ConstraintSetCount"/>
<property name="DesignMode" value="RTL" type="DesignMode"/>
<property name="SynthesisStrategy" value="Vivado Synthesis Defaults" type="SynthesisStrategy"/>
<property name="ImplStrategy" value="Vivado Implementation Defaults" type="ImplStrategy"/>
</item>
<item name="Java Command Handlers">
<property name="AddSources" value="4" type="JavaHandler"/>
<property name="CoreView" value="3" type="JavaHandler"/>
<property name="CustomizeCore" value="4" type="JavaHandler"/>
<property name="RecustomizeCore" value="1" type="JavaHandler"/>
<property name="RunBitgen" value="3" type="JavaHandler"/>
<property name="RunImplementation" value="1" type="JavaHandler"/>
<property name="RunSynthesis" value="1" type="JavaHandler"/>
<property name="ToolsSettings" value="2" type="JavaHandler"/>
</item>
<item name="Gui Handlers">
<property name="AddSrcWizard_SPECIFY_HDL_NETLIST_BLOCK_DESIGN" value="1" type="GuiHandlerData"/>
<property name="AddSrcWizard_SPECIFY_SIMULATION_SPECIFIC_HDL_FILES" value="1" type="GuiHandlerData"/>
<property name="BaseDialog_APPLY" value="1" type="GuiHandlerData"/>
<property name="BaseDialog_CANCEL" value="4" type="GuiHandlerData"/>
<property name="BaseDialog_OK" value="39" type="GuiHandlerData"/>
<property name="BaseDialog_YES" value="2" type="GuiHandlerData"/>
<property name="CoreTreeTablePanel_CORE_TREE_TABLE" value="21" type="GuiHandlerData"/>
<property name="CreateSrcFileDialog_FILE_NAME" value="18" type="GuiHandlerData"/>
<property name="CustomizeErrorDialog_MESSAGES" value="4" type="GuiHandlerData"/>
<property name="CustomizeErrorDialog_OK" value="2" type="GuiHandlerData"/>
<property name="DefineModulesDialog_NEW_SOURCE_FILES" value="5" type="GuiHandlerData"/>
<property name="ExpRunTreePanel_EXP_RUN_TREE_TABLE" value="1" type="GuiHandlerData"/>
<property name="FileSetPanel_FILE_SET_PANEL_TREE" value="196" type="GuiHandlerData"/>
<property name="FlowNavigatorTreePanel_FLOW_NAVIGATOR_TREE" value="14" type="GuiHandlerData"/>
<property name="HJFileChooserHelpers_JUMP_TO_RECENT_PROJECT_DIRECTORY" value="3" type="GuiHandlerData"/>
<property name="IPCoreView_TABBED_PANE" value="2" type="GuiHandlerData"/>
<property name="MainMenuMgr_TOOLS" value="4" type="GuiHandlerData"/>
<property name="MessageWithOptionDialog_DONT_SHOW_THIS_DIALOG_AGAIN" value="1" type="GuiHandlerData"/>
<property name="NumJobsChooser_NUMBER_OF_JOBS" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_AUTO_UPDATE_HIER" value="3" type="GuiHandlerData"/>
<property name="PACommandNames_RUN_BITGEN" value="1" type="GuiHandlerData"/>
<property name="PAViews_CODE" value="2" type="GuiHandlerData"/>
<property name="PAViews_IP_CATALOG" value="1" type="GuiHandlerData"/>
<property name="PAViews_PROJECT_SUMMARY" value="1" type="GuiHandlerData"/>
<property name="RDICommands_CUSTOM_COMMANDS" value="1" type="GuiHandlerData"/>
<property name="RDICommands_SETTINGS" value="2" type="GuiHandlerData"/>
<property name="SettingsDialog_OPTIONS_TREE" value="2" type="GuiHandlerData"/>
<property name="SettingsEditorPage_ENTER_COMMAND_LINE_FOR_CUSTOM" value="2" type="GuiHandlerData"/>
<property name="SettingsEditorPage_USE_THIS_DROP_DOWN_LIST_BOX_TO_SELECT" value="2" type="GuiHandlerData"/>
<property name="SimpleOutputProductDialog_GENERATE_OUTPUT_PRODUCTS_IMMEDIATELY" value="4" type="GuiHandlerData"/>
<property name="SrcChooserPanel_CREATE_FILE" value="17" type="GuiHandlerData"/>
<property name="SrcMenu_IP_HIERARCHY" value="2" type="GuiHandlerData"/>
<property name="XPG_CoeFileWidgdet_BROWSE" value="3" type="GuiHandlerData"/>
</item>
<item name="Other">
<property name="GuiMode" value="1" type="GuiMode"/>
<property name="BatchMode" value="0" type="BatchMode"/>
<property name="TclMode" value="0" type="TclMode"/>
</item>
</section>
</application>
</document>
