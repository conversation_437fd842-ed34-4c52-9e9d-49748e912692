#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Thu Jul  3 14:37:08 2025
# Process ID: 17528
# Current directory: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/miniRV_demo
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent18392 C:\Users\<USER>\Desktop\实验\cpu设计\lab2\miniRV_demo\miniRV_demo.xpr
# Log file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/miniRV_demo/vivado.log
# Journal file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/miniRV_demo\vivado.jou
#-----------------------------------------------------------
start_gui
open_project C:/Users/<USER>/Desktop/实验/cpu设计/lab2/miniRV_demo/miniRV_demo.xpr
INFO: [Project 1-313] Project file moved from 'E:/miniRV_demo' since last save.
Scanning sources...
Finished scanning sources
WARNING: [filemgmt 56-2] IPUserFilesDir: Could not find the directory 'C:/Users/<USER>/Desktop/实验/cpu设计/lab2/miniRV_demo/miniRV_demo.ip_user_files', nor could it be found using path 'E:/miniRV_demo/miniRV_demo.ip_user_files'.
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'E:/Vivado/2018.3/data/ip'.
update_compile_order -fileset sources_1
