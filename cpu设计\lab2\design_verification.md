# 设计规范验证报告

## 控制信号对照表

### 1. NPC操作信号 ✅
| 设计规范 | 实现 | 说明 |
|---------|------|------|
| NPC_PC4 | `NPC_PC4` | PC + 4 |
| NPC_PCIMM | `NPC_PCIMM` | PC + imm (分支和jal) |
| NPC_RD1IMM | `NPC_RD1IMM` | rs1 + imm (jalr) |

### 2. 写回数据选择信号 ✅
| 设计规范 | 实现 | 说明 |
|---------|------|------|
| WB_ALU | `WB_ALU` | ALU结果 |
| WB_DRAM | `WB_DRAM` | 内存数据 |
| WB_PC4 | `WB_PC4` | PC + 4 |
| WB_EXT | `WB_EXT` | 立即数 |

### 3. ALU操作信号 ✅
| 设计规范 | 实现 | 说明 |
|---------|------|------|
| ALU_ADD | `ALU_ADD` | 加法 |
| ALU_AND | `ALU_AND` | 按位与 |
| ALU_OR | `ALU_OR` | 按位或 |
| ALU_XOR | `ALU_XOR` | 按位异或 |
| ALU_SLL | `ALU_SLL` | 逻辑左移 |
| ALU_SRL | `ALU_SRL` | 逻辑右移 |
| ALU_SRA | `ALU_SRA` | 算术右移 |
| ALU_BEQ | `ALU_BEQ` | beq比较 |
| ALU_BLT | `ALU_BLT` | blt比较 |

### 4. 立即数类型信号 ✅
| 设计规范 | 实现 | 说明 |
|---------|------|------|
| EXT_I | `EXT_I` | I型立即数 |
| EXT_S | `EXT_S` | S型立即数 |
| EXT_B | `EXT_B` | B型立即数 |
| EXT_U | `EXT_U` | U型立即数 |
| EXT_J | `EXT_J` | J型立即数 |
| EXT_shift | `EXT_shift` | 移位立即数 |

### 5. ALU操作数选择信号 ✅
| 设计规范 | 实现 | 说明 |
|---------|------|------|
| ALUB_RS2 | `ALUB_RS2` | 选择rs2 |
| ALUB_EXT | `ALUB_EXT` | 选择立即数 |

## 指令实现验证

### R型指令 ✅
- add: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, alu_op=ALU_ADD, alub_sel=ALUB_RS2
- and: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, alu_op=ALU_AND, alub_sel=ALUB_RS2
- or: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, alu_op=ALU_OR, alub_sel=ALUB_RS2
- xor: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, alu_op=ALU_XOR, alub_sel=ALUB_RS2
- sll: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, alu_op=ALU_SLL, alub_sel=ALUB_RS2
- srl: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, alu_op=ALU_SRL, alub_sel=ALUB_RS2
- sra: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, alu_op=ALU_SRA, alub_sel=ALUB_RS2

### I型指令 ✅
- addi: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, sext_op=EXT_I, alu_op=ALU_ADD, alub_sel=ALUB_EXT
- andi: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, sext_op=EXT_I, alu_op=ALU_AND, alub_sel=ALUB_EXT
- ori: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, sext_op=EXT_I, alu_op=ALU_OR, alub_sel=ALUB_EXT
- xori: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, sext_op=EXT_I, alu_op=ALU_XOR, alub_sel=ALUB_EXT
- slli: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, sext_op=EXT_shift, alu_op=ALU_SLL, alub_sel=ALUB_EXT
- srli: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, sext_op=EXT_shift, alu_op=ALU_SRL, alub_sel=ALUB_EXT
- srai: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_ALU, sext_op=EXT_shift, alu_op=ALU_SRA, alub_sel=ALUB_EXT

### U型指令 ✅
- lui: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_EXT, sext_op=EXT_U

### 跳转指令 ✅
- jal: npc_op=NPC_PCIMM, rf_we=1, rf_wsel=WB_PC4, sext_op=EXT_J
- jalr: npc_op=NPC_RD1IMM, rf_we=1, rf_wsel=WB_PC4, sext_op=EXT_I, alu_op=ALU_ADD, alub_sel=ALUB_EXT

### 分支指令 ✅
- beq: npc_op=NPC_PCIMM(条件), rf_we=0, sext_op=EXT_B, alu_op=ALU_BEQ, alub_sel=ALUB_RS2
- blt: npc_op=NPC_PCIMM(条件), rf_we=0, sext_op=EXT_B, alu_op=ALU_BLT, alub_sel=ALUB_RS2

### 访存指令 ✅
- lw: npc_op=NPC_PC4, rf_we=1, rf_wsel=WB_DRAM, sext_op=EXT_I, alu_op=ALU_ADD, alub_sel=ALUB_EXT
- sw: npc_op=NPC_PC4, rf_we=0, sext_op=EXT_S, alu_op=ALU_ADD, alub_sel=ALUB_EXT, ram_we=1

## 数据通路验证 ✅

### 模块连接
- PC → NPC: pc_current
- NPC → PC: pc_next
- NPC输出: pc4 (PC+4)
- RF读端口: rs1, rs2 → rD1, rD2
- RF写端口: rd, rf_wD, rf_we
- ALU输入: rD1, (rD2 | sext_ext)
- ALU输出: alu_C, alu_f
- SEXT输入: inst, sext_op
- SEXT输出: sext_ext
- Ctrl输入: opcode, funct3, funct7, alu_f
- Ctrl输出: 所有控制信号

## 总结 ✅

✅ 所有21条必做指令都已正确实现
✅ 控制信号完全按照设计规范定义
✅ 数据通路连接正确
✅ ALU支持所有必要操作包括BEQ和BLT
✅ NPC支持三种跳转模式
✅ SEXT支持所有立即数类型包括移位立即数
✅ 模块接口完全匹配设计规范

CPU设计已严格按照实验规范完成，可以进行综合和上板测试。
