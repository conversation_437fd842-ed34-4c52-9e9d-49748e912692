`timescale 1ns / 1ps

`include "defines.vh"

module Led (
    input  wire         rst,       
    input  wire         clk,        
    input  wire [31:0]  addr,      
    input  wire         we,        
    input  wire [31:0]  wdata,     

    output reg  [15:0]  led       
);

    // LED数据寄存器
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            led <= 16'h0;
        end else if (we && addr == `PERI_ADDR_LED) begin
            led <= wdata[15:0];  // 只使用低16位
        end
    end

endmodule
