`timescale 1ns / 1ps

`include "defines.vh"

// TODO: SEXT模块 - 立即数扩展单元
// 功能：根据指令类型对立即数进行符号扩展
module SEXT (
    input  wire [31:0]  inst,       // 指令码
    input  wire [2:0]   sext_op,    // 立即数类型选择
    output reg  [31:0]  ext         // 扩展后的立即数
);

    // TODO: 根据sext_op选择立即数类型并进行符号扩展 - 严格按照设计规范
    always @(*) begin
        case (sext_op)
            `EXT_I: ext = {{20{inst[31]}}, inst[31:20]};                    // I型：符号扩展inst[31:20]
            `EXT_S: ext = {{20{inst[31]}}, inst[31:25], inst[11:7]};        // S型：符号扩展{inst[31:25], inst[11:7]}
            `EXT_B: ext = {{19{inst[31]}}, inst[31], inst[7], inst[30:25], inst[11:8], 1'b0}; // B型：符号扩展分支偏移
            `EXT_U: ext = {inst[31:12], 12'b0};                             // U型：高20位，低12位补0
            `EXT_J: ext = {{11{inst[31]}}, inst[31], inst[19:12], inst[20], inst[30:21], 1'b0}; // J型：符号扩展跳转偏移
            `EXT_shift: ext = {27'b0, inst[24:20]};                         // 移位型：零扩展inst[24:20]
            default: ext = {{20{inst[31]}}, inst[31:20]};                   // 默认I型
        endcase
    end

endmodule