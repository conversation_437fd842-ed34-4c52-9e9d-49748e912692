// Annotate this macro before synthesis
// `define RUN_TRACE

// miniRV指令集操作码定义 (严格按照实验文档)
`define OP_LUI      7'b0110111  // Load Upper Immediate
`define OP_JAL      7'b1101111  // Jump and Link
`define OP_JALR     7'b1100111  // Jump and Link Register
`define OP_BRANCH   7'b1100011  // Branch instructions (beq, blt)
`define OP_LOAD     7'b0000011  // Load instructions (lw)
`define OP_STORE    7'b0100011  // Store instructions (sw)
`define OP_IMM      7'b0010011  // Immediate arithmetic (addi, andi, ori, xori, slli, srli, srai)
`define OP_REG      7'b0110011  // Register arithmetic (add, and, or, xor, sll, srl, sra)

// ALU操作码定义
`define ALU_ADD     4'b0000     // 加法
`define ALU_SUB     4'b0001     // 减法
`define ALU_AND     4'b0010     // 按位与
`define ALU_OR      4'b0011     // 按位或
`define ALU_XOR     4'b0100     // 按位异或
`define ALU_SLL     4'b0101     // 逻辑左移
`define ALU_SRL     4'b0110     // 逻辑右移
`define ALU_SRA     4'b0111     // 算术右移
`define ALU_SLT     4'b1000     // 有符号比较
`define ALU_SLTU    4'b1001     // 无符号比较

// 立即数类型定义
`define IMM_I       3'b000      // I型立即数 (addi, lw, jalr等)
`define IMM_S       3'b001      // S型立即数 (sw等)
`define IMM_B       3'b010      // B型立即数 (beq, blt等)
`define IMM_U       3'b011      // U型立即数 (lui等)
`define IMM_J       3'b100      // J型立即数 (jal等)

// NPC操作类型定义
`define NPC_PC4     2'b00       // PC + 4 (顺序执行)
`define NPC_BRANCH  2'b01       // PC + imm (分支跳转)
`define NPC_JUMP    2'b10       // ALU结果 (jalr)
`define NPC_JAL     2'b11       // PC + imm (jal)

// 写回数据选择信号
`define WB_ALU      2'b00       // ALU结果
`define WB_MEM      2'b01       // 内存数据
`define WB_PC4      2'b10       // PC+4 (jal/jalr)
`define WB_IMM      2'b11       // 立即数 (lui)

// 外设I/O接口电路的端口地址
`define PERI_ADDR_DIG   32'hFFFF_F000
`define PERI_ADDR_LED   32'hFFFF_F060
`define PERI_ADDR_SW    32'hFFFF_F070
`define PERI_ADDR_BTN   32'hFFFF_F078
