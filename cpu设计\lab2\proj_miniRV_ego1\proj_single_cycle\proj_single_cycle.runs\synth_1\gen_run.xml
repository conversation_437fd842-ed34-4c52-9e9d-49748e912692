<?xml version="1.0" encoding="UTF-8"?>
<GenRun Id="synth_1" LaunchPart="xc7a35tcsg324-1" LaunchTime="1751533848">
  <File Type="PA-TCL" Name="miniRV_SoC.tcl"/>
  <File Type="RDS-PROPCONSTRS" Name="miniRV_SoC_drc_synth.rpt"/>
  <File Type="REPORTS-TCL" Name="miniRV_SoC_reports.tcl"/>
  <File Type="RDS-RDS" Name="miniRV_SoC.vds"/>
  <File Type="RDS-UTIL" Name="miniRV_SoC_utilization_synth.rpt"/>
  <File Type="RDS-UTIL-PB" Name="miniRV_SoC_utilization_synth.pb"/>
  <File Type="RDS-DCP" Name="miniRV_SoC.dcp"/>
  <File Type="VDS-TIMINGSUMMARY" Name="miniRV_SoC_timing_summary_synth.rpt"/>
  <File Type="VDS-TIMING-PB" Name="miniRV_SoC_timing_summary_synth.pb"/>
  <FileSet Name="sources" Type="DesignSrcs" RelSrcDir="$PSRCDIR/sources_1">
    <Filter Type="Srcs"/>
    <File Path="$PSRCDIR/sources_1/new/defines.vh">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/ALU.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Bridge.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Button.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Ctrl.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Dig.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Led.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/NPC.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/PC.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/RF.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/SEXT.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Switch.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Timer.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/myCPU.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/miniRV_SoC.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/test.coe">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="DesignMode" Val="RTL"/>
      <Option Name="TopModule" Val="miniRV_SoC"/>
      <Option Name="TopAutoSet" Val="TRUE"/>
    </Config>
  </FileSet>
  <FileSet Name="constrs_in" Type="Constrs" RelSrcDir="$PSRCDIR/constrs_1">
    <Filter Type="Constrs"/>
    <File Path="$PSRCDIR/constrs_1/new/miniRV_clock.xdc">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/constrs_1/new/miniRV_SoC.xdc">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="TargetConstrsFile" Val="$PSRCDIR/constrs_1/new/miniRV_SoC.xdc"/>
      <Option Name="ConstrsType" Val="XDC"/>
    </Config>
  </FileSet>
  <FileSet Name="utils" Type="Utils" RelSrcDir="$PSRCDIR/utils_1">
    <Filter Type="Utils"/>
    <Config>
      <Option Name="TopAutoSet" Val="TRUE"/>
    </Config>
  </FileSet>
  <Strategy Version="1" Minor="2">
    <StratHandle Name="Vivado Synthesis Defaults" Flow="Vivado Synthesis 2018">
      <Desc>Vivado Synthesis Defaults</Desc>
    </StratHandle>
    <Step Id="synth_design"/>
  </Strategy>
  <BlockFileSet Type="BlockSrcs" Name="DRAM"/>
  <BlockFileSet Type="BlockSrcs" Name="cpuclk"/>
  <BlockFileSet Type="BlockSrcs" Name="IROM"/>
</GenRun>
