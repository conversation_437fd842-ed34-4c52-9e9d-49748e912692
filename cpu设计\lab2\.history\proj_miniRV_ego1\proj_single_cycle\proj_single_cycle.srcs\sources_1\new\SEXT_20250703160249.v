`timescale 1ns / 1ps

`include "defines.vh"

// TODO: SEXT模块 - 立即数符号扩展单元
// 功能：对不同类型的立即数进行符号扩展
module SEXT (
    input  wire [31:0]  inst,       // 指令
    input  wire [2:0]   sext_op,    // 立即数类型选择
    output reg  [31:0]  ext         // 扩展后的立即数
);

    // TODO: 根据sext_op选择立即数类型并进行符号扩展
    always @(*) begin
        case (sext_op)
            `IMM_I: begin   // I型立即数：inst[31:20]
                ext = {{20{inst[31]}}, inst[31:20]};
            end
            `IMM_S: begin   // S型立即数：{inst[31:25], inst[11:7]}
                ext = {{20{inst[31]}}, inst[31:25], inst[11:7]};
            end
            `IMM_B: begin   // B型立即数：{inst[31], inst[7], inst[30:25], inst[11:8], 1'b0}
                ext = {{19{inst[31]}}, inst[31], inst[7], inst[30:25], inst[11:8], 1'b0};
            end
            `IMM_U: begin   // U型立即数：{inst[31:12], 12'b0}
                ext = {inst[31:12], 12'b0};
            end
            `IMM_J: begin   // J型立即数：{inst[31], inst[19:12], inst[20], inst[30:21], 1'b0}
                ext = {{11{inst[31]}}, inst[31], inst[19:12], inst[20], inst[30:21], 1'b0};
            end
            default: begin
                ext = 32'h0;
            end
        endcase
    end

endmodule
