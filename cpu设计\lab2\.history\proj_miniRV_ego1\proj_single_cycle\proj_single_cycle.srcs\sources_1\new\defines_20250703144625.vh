// Annotate this macro before synthesis
// `define RUN_TRACE

// RISC-V Instruction Opcodes
`define OP_LUI      7'b0110111  // Load Upper Immediate
`define OP_AUIPC    7'b0010111  // Add Upper Immediate to PC
`define OP_JAL      7'b1101111  // Jump and Link
`define OP_JALR     7'b1100111  // Jump and Link Register
`define OP_BRANCH   7'b1100011  // Branch instructions
`define OP_LOAD     7'b0000011  // Load instructions
`define OP_STORE    7'b0100011  // Store instructions
`define OP_IMM      7'b0010011  // Immediate arithmetic
`define OP_REG      7'b0110011  // Register arithmetic

// ALU Operations
`define ALU_ADD     4'b0000
`define ALU_SUB     4'b0001
`define ALU_AND     4'b0010
`define ALU_OR      4'b0011
`define ALU_XOR     4'b0100
`define ALU_SLL     4'b0101
`define ALU_SRL     4'b0110
`define ALU_SRA     4'b0111
`define ALU_SLT     4'b1000
`define ALU_SLTU    4'b1001

// Immediate Types
`define IMM_I       3'b000  // I-type immediate
`define IMM_S       3'b001  // S-type immediate
`define IMM_B       3'b010  // B-type immediate
`define IMM_U       3'b011  // U-type immediate
`define IMM_J       3'b100  // J-type immediate

// 外设I/O接口电路的端口地址
`define PERI_ADDR_DIG   32'hFFFF_F000
`define PERI_ADDR_LED   32'hFFFF_F060
`define PERI_ADDR_SW    32'hFFFF_F070
`define PERI_ADDR_BTN   32'hFFFF_F078
