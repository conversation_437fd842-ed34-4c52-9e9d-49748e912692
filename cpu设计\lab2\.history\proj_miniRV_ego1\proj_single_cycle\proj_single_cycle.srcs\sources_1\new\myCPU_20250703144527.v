`timescale 1ns / 1ps

`include "defines.vh"

module myCPU (
    input  wire         cpu_rst,
    input  wire         cpu_clk,

    // Interface to IROM
`ifdef RUN_TRACE
    output wire [15:0]  inst_addr,
`else
    output wire [13:0]  inst_addr,
`endif
    input  wire [31:0]  inst,

    // Interface to Bridge
    output wire [31:0]  Bus_addr,
    input  wire [31:0]  Bus_rdata,
    output wire         Bus_we,
    output wire [31:0]  Bus_wdata

`ifdef RUN_TRACE
    ,// Debug Interface
    output wire         debug_wb_have_inst,
    output wire [31:0]  debug_wb_pc,
    output              debug_wb_ena,
    output wire [ 4:0]  debug_wb_reg,
    output wire [31:0]  debug_wb_value
`endif
);

    // Internal wires
    wire [31:0] pc_current;
    wire [31:0] pc_next;
    wire [31:0] pc_plus4;
    wire [31:0] pc_branch;
    wire [31:0] pc_jump;

    // Register file signals
    wire [31:0] rf_rdata1;
    wire [31:0] rf_rdata2;
    wire [31:0] rf_wdata;
    wire        rf_we;
    wire [4:0]  rf_waddr;

    // ALU signals
    wire [31:0] alu_result;
    wire [31:0] alu_src1;
    wire [31:0] alu_src2;
    wire [3:0]  alu_op;
    wire        alu_zero;

    // Immediate extension
    wire [31:0] imm_ext;

    // Control signals
    wire        branch;
    wire        jump;
    wire        mem_read;
    wire        mem_write;
    wire        reg_write;
    wire        mem_to_reg;
    wire        alu_src;
    wire [1:0]  pc_src;
    wire [2:0]  imm_sel;

    // Instruction fields
    wire [6:0]  opcode = inst[6:0];
    wire [4:0]  rd     = inst[11:7];
    wire [2:0]  funct3 = inst[14:12];
    wire [4:0]  rs1    = inst[19:15];
    wire [4:0]  rs2    = inst[24:20];
    wire [6:0]  funct7 = inst[31:25];

    // PC logic
    assign pc_plus4 = pc_current + 32'd4;
    assign pc_branch = pc_current + imm_ext;
    assign pc_jump = alu_result;

    always @(*) begin
        case (pc_src)
            2'b00: pc_next = pc_plus4;      // Normal increment
            2'b01: pc_next = pc_branch;     // Branch
            2'b10: pc_next = pc_jump;       // Jump (JAL/JALR)
            default: pc_next = pc_plus4;
        endcase
    end

    // PC register
    reg [31:0] pc_reg;
    always @(posedge cpu_clk or posedge cpu_rst) begin
        if (cpu_rst)
            pc_reg <= 32'h0000_0000;
        else
            pc_reg <= pc_next;
    end
    assign pc_current = pc_reg;

    // Instruction address output
`ifdef RUN_TRACE
    assign inst_addr = pc_current[15:0];
`else
    assign inst_addr = pc_current[15:2];  // Word address for 14-bit output
`endif

    // Register File (32 x 32-bit registers)
    reg [31:0] registers [31:0];
    integer i;

    always @(posedge cpu_clk) begin
        if (cpu_rst) begin
            for (i = 0; i < 32; i = i + 1)
                registers[i] <= 32'b0;
        end else if (rf_we && rf_waddr != 5'b0) begin
            registers[rf_waddr] <= rf_wdata;
        end
    end

    assign rf_rdata1 = (rs1 == 5'b0) ? 32'b0 : registers[rs1];
    assign rf_rdata2 = (rs2 == 5'b0) ? 32'b0 : registers[rs2];
    assign rf_waddr = rd;

`ifdef RUN_TRACE
    // Debug Interface
    assign debug_wb_have_inst = ~cpu_rst;
    assign debug_wb_pc        = pc_current;
    assign debug_wb_ena       = rf_we;
    assign debug_wb_reg       = rf_waddr;
    assign debug_wb_value     = rf_wdata;
`endif

endmodule
