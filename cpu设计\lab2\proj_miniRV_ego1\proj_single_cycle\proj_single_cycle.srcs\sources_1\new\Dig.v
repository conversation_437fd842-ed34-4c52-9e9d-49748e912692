`timescale 1ns / 1ps

`include "defines.vh"

// TODO: 数码管接口模块
// 功能：控制8位7段数码管显示，支持16进制数显示
module Dig (
    input  wire         rst,        // 复位信号
    input  wire         clk,        // 时钟信号
    input  wire [31:0]  addr,       // 地址信号
    input  wire         we,         // 写使能信号
    input  wire [31:0]  wdata,      // 写数据信号

    // 数码管硬件接口 (EGO1开发板)
    output reg  [ 7:0]  dig_en,     // 数码管位选信号
    output reg          DN_A0, DN_A1,  // A段
    output reg          DN_B0, DN_B1,  // B段
    output reg          DN_C0, DN_C1,  // C段
    output reg          DN_D0, DN_D1,  // D段
    output reg          DN_E0, DN_E1,  // E段
    output reg          DN_F0, DN_F1,  // F段
    output reg          DN_G0, DN_G1,  // G段
    output reg          DN_DP0, DN_DP1 // 小数点
);

    // TODO: 数据寄存器，存储要显示的32位数据
    reg [31:0] display_data;

    // TODO: 扫描计数器，用于动态扫描数码管
    reg [2:0] scan_cnt;
    reg [15:0] scan_clk_cnt;

    // TODO: 当前扫描位的4位数据
    wire [3:0] current_digit = (scan_cnt == 3'd0) ? display_data[3:0]   :
                               (scan_cnt == 3'd1) ? display_data[7:4]   :
                               (scan_cnt == 3'd2) ? display_data[11:8]  :
                               (scan_cnt == 3'd3) ? display_data[15:12] :
                               (scan_cnt == 3'd4) ? display_data[19:16] :
                               (scan_cnt == 3'd5) ? display_data[23:20] :
                               (scan_cnt == 3'd6) ? display_data[27:24] :
                                                     display_data[31:28];

    // TODO: 7段译码器 - 将4位16进制数转换为7段显示码
    reg [6:0] seg_code;
    always @(*) begin
        case (current_digit)
            4'h0: seg_code = 7'b1111110;  // 0
            4'h1: seg_code = 7'b0110000;  // 1
            4'h2: seg_code = 7'b1101101;  // 2
            4'h3: seg_code = 7'b1111001;  // 3
            4'h4: seg_code = 7'b0110011;  // 4
            4'h5: seg_code = 7'b1011011;  // 5
            4'h6: seg_code = 7'b1011111;  // 6
            4'h7: seg_code = 7'b1110000;  // 7
            4'h8: seg_code = 7'b1111111;  // 8
            4'h9: seg_code = 7'b1111011;  // 9
            4'hA: seg_code = 7'b1110111;  // A
            4'hB: seg_code = 7'b0011111;  // b
            4'hC: seg_code = 7'b1001110;  // C
            4'hD: seg_code = 7'b0111101;  // d
            4'hE: seg_code = 7'b1001111;  // E
            4'hF: seg_code = 7'b1000111;  // F
        endcase
    end

    // TODO: 写数据寄存器
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            display_data <= 32'h0;
        end else if (we && addr == `PERI_ADDR_DIG) begin
            display_data <= wdata;
        end
    end

    // TODO: 扫描时钟分频 (约1KHz扫描频率)
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            scan_clk_cnt <= 16'h0;
            scan_cnt <= 3'h0;
        end else begin
            if (scan_clk_cnt >= 16'd25000) begin  // 25MHz/25000 = 1KHz
                scan_clk_cnt <= 16'h0;
                scan_cnt <= scan_cnt + 1'b1;
            end else begin
                scan_clk_cnt <= scan_clk_cnt + 1'b1;
            end
        end
    end

    // TODO: 位选信号生成 (高电平有效)
    always @(*) begin
        dig_en = 8'h00;
        dig_en[scan_cnt] = 1'b1;
    end

    // TODO: 段选信号输出 (高电平有效)
    always @(*) begin
        {DN_G0, DN_F0, DN_E0, DN_D0, DN_C0, DN_B0, DN_A0} = seg_code;
        {DN_G1, DN_F1, DN_E1, DN_D1, DN_C1, DN_B1, DN_A1} = seg_code;
        DN_DP0 = 1'b0;  // 小数点不显示
        DN_DP1 = 1'b0;
    end

endmodule
