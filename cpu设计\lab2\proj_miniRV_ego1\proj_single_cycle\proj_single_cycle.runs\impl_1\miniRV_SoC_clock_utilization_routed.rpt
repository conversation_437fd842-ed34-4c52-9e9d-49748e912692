Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
----------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Thu Jul  3 18:34:36 2025
| Host         : L running 64-bit major release  (build 9200)
| Command      : report_clock_utilization -file miniRV_SoC_clock_utilization_routed.rpt
| Design       : miniRV_SoC
| Device       : 7a35t-csg324
| Speed File   : -1  PRODUCTION 1.23 2018-06-13
----------------------------------------------------------------------------------------

Clock Utilization Report

Table of Contents
-----------------
1. Clock Primitive Utilization
2. Global Clock Resources
3. Global Clock Source Details
4. Clock Regions: Key Resource Utilization
5. Clock Regions : Global Clock Summary
6. Device Cell Placement Summary for Global Clock g0
7. Device Cell Placement Summary for Global Clock g1
8. Device Cell Placement Summary for Global Clock g2
9. Clock Region Cell Placement per Global Clock: Region X0Y0
10. Clock Region Cell Placement per Global Clock: Region X1Y0
11. Clock Region Cell Placement per Global Clock: Region X0Y1
12. Clock Region Cell Placement per Global Clock: Region X1Y1

1. Clock Primitive Utilization
------------------------------

+----------+------+-----------+-----+--------------+--------+
| Type     | Used | Available | LOC | Clock Region | Pblock |
+----------+------+-----------+-----+--------------+--------+
| BUFGCTRL |    3 |        32 |   0 |            0 |      0 |
| BUFH     |    0 |        72 |   0 |            0 |      0 |
| BUFIO    |    0 |        20 |   0 |            0 |      0 |
| BUFMR    |    0 |        10 |   0 |            0 |      0 |
| BUFR     |    0 |        20 |   0 |            0 |      0 |
| MMCM     |    0 |         5 |   0 |            0 |      0 |
| PLL      |    1 |         5 |   0 |            0 |      0 |
+----------+------+-----------+-----+--------------+--------+


2. Global Clock Resources
-------------------------

+-----------+-----------+-----------------+------------+---------------+--------------+-------------------+-------------+-----------------+--------------+-----------------+---------------------------+---------------------------------+
| Global Id | Source Id | Driver Type/Pin | Constraint | Site          | Clock Region | Load Clock Region | Clock Loads | Non-Clock Loads | Clock Period | Clock           | Driver Pin                | Net                             |
+-----------+-----------+-----------------+------------+---------------+--------------+-------------------+-------------+-----------------+--------------+-----------------+---------------------------+---------------------------------+
| g0        | src0      | BUFG/O          | None       | BUFGCTRL_X0Y0 | n/a          |                 4 |        2370 |               0 |       40.000 | clk_out1_cpuclk | cpu_clk_BUFG_inst/O       | cpu_clk_BUFG                    |
| g1        | src1      | BUFG/O          | None       | BUFGCTRL_X0Y1 | n/a          |                 1 |           1 |               0 |       40.000 | clkfbout_cpuclk | Clkgen/inst/clkf_buf/O    | Clkgen/inst/clkfbout_buf_cpuclk |
| g2        | src1      | BUFG/O          | None       | BUFGCTRL_X0Y2 | n/a          |                 1 |           0 |               1 |       40.000 | clk_out1_cpuclk | Clkgen/inst/clkout1_buf/O | Clkgen/inst/clk_out1            |
+-----------+-----------+-----------------+------------+---------------+--------------+-------------------+-------------+-----------------+--------------+-----------------+---------------------------+---------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)


3. Global Clock Source Details
------------------------------

+-----------+-----------+--------------------+------------+----------------+--------------+-------------+-----------------+---------------------+-----------------+-------------------------------------+-----------------------------+
| Source Id | Global Id | Driver Type/Pin    | Constraint | Site           | Clock Region | Clock Loads | Non-Clock Loads | Source Clock Period | Source Clock    | Driver Pin                          | Net                         |
+-----------+-----------+--------------------+------------+----------------+--------------+-------------+-----------------+---------------------+-----------------+-------------------------------------+-----------------------------+
| src0      | g0        | LUT2/O             | None       | SLICE_X35Y46   | X0Y0         |           1 |               0 |              40.000 | clk_out1_cpuclk | cpu_clk_BUFG_inst_i_1/O             | cpu_clk                     |
| src1      | g2        | PLLE2_ADV/CLKOUT0  | None       | PLLE2_ADV_X0Y0 | X0Y0         |           1 |               0 |              40.000 | clk_out1_cpuclk | Clkgen/inst/plle2_adv_inst/CLKOUT0  | Clkgen/inst/clk_out1_cpuclk |
| src1      | g1        | PLLE2_ADV/CLKFBOUT | None       | PLLE2_ADV_X0Y0 | X0Y0         |           1 |               0 |              40.000 | clkfbout_cpuclk | Clkgen/inst/plle2_adv_inst/CLKFBOUT | Clkgen/inst/clkfbout_cpuclk |
+-----------+-----------+--------------------+------------+----------------+--------------+-------------+-----------------+---------------------+-----------------+-------------------------------------+-----------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)


4. Clock Regions: Key Resource Utilization
------------------------------------------

+-------------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+
|                   | Global Clock |     BUFRs    |    BUFMRs    |    BUFIOs    |     MMCM     |      PLL     |      GT      |      PCI     |    ILOGIC    |    OLOGIC    |      FF      |     LUTM     |    RAMB18    |    RAMB36    |    DSP48E2   |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
| Clock Region Name | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
| X0Y0              |    3 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    1 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |   74 |  1200 |    0 |   400 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y0              |    1 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |  199 |  1500 |    0 |   450 |    0 |    40 |    0 |    20 |    0 |    20 |
| X0Y1              |    1 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |   25 |  1200 |    0 |   400 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y1              |    1 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |   12 |  1500 |    0 |   450 |    0 |    40 |    0 |    20 |    0 |    20 |
| X0Y2              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  1800 |    0 |   400 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y2              |    0 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     4 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |   950 |    0 |   300 |    0 |    10 |    0 |     5 |    0 |    20 |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
* Global Clock column represents track count; while other columns represents cell counts


5. Clock Regions : Global Clock Summary
---------------------------------------

All Modules
+----+----+----+
|    | X0 | X1 |
+----+----+----+
| Y2 |  0 |  0 |
| Y1 |  1 |  1 |
| Y0 |  3 |  1 |
+----+----+----+


6. Device Cell Placement Summary for Global Clock g0
----------------------------------------------------

+-----------+-----------------+-------------------+-----------------+-------------+----------------+-------------+----------+----------------+----------+--------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock           | Period (ns) | Waveform (ns)  | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net          |
+-----------+-----------------+-------------------+-----------------+-------------+----------------+-------------+----------+----------------+----------+--------------+
| g0        | BUFG/O          | n/a               | clk_out1_cpuclk |      40.000 | {0.000 20.000} |         310 |        0 |              0 |        0 | cpu_clk_BUFG |
+-----------+-----------------+-------------------+-----------------+-------------+----------------+-------------+----------+----------------+----------+--------------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+-----+------+
|    | X0  | X1   |
+----+-----+------+
| Y2 |   0 |    0 |
| Y1 |  25 |   12 |
| Y0 |  74 |  199 |
+----+-----+------+


7. Device Cell Placement Summary for Global Clock g1
----------------------------------------------------

+-----------+-----------------+-------------------+-----------------+-------------+----------------+-------------+----------+----------------+----------+---------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock           | Period (ns) | Waveform (ns)  | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                             |
+-----------+-----------------+-------------------+-----------------+-------------+----------------+-------------+----------+----------------+----------+---------------------------------+
| g1        | BUFG/O          | n/a               | clkfbout_cpuclk |      40.000 | {0.000 20.000} |           0 |        0 |              1 |        0 | Clkgen/inst/clkfbout_buf_cpuclk |
+-----------+-----------------+-------------------+-----------------+-------------+----------------+-------------+----------+----------------+----------+---------------------------------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+----+----+
|    | X0 | X1 |
+----+----+----+
| Y2 |  0 |  0 |
| Y1 |  0 |  0 |
| Y0 |  1 |  0 |
+----+----+----+


8. Device Cell Placement Summary for Global Clock g2
----------------------------------------------------

+-----------+-----------------+-------------------+-----------------+-------------+----------------+-------------+----------+----------------+----------+----------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock           | Period (ns) | Waveform (ns)  | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                  |
+-----------+-----------------+-------------------+-----------------+-------------+----------------+-------------+----------+----------------+----------+----------------------+
| g2        | BUFG/O          | n/a               | clk_out1_cpuclk |      40.000 | {0.000 20.000} |           1 |        0 |              0 |        0 | Clkgen/inst/clk_out1 |
+-----------+-----------------+-------------------+-----------------+-------------+----------------+-------------+----------+----------------+----------+----------------------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+----+----+
|    | X0 | X1 |
+----+----+----+
| Y2 |  0 |  0 |
| Y1 |  0 |  0 |
| Y0 |  1 |  0 |
+----+----+----+


9. Clock Region Cell Placement per Global Clock: Region X0Y0
------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+---------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                             |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+---------------------------------+
| g0        | n/a   | BUFG/O          | None       |          74 |               0 | 74 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | cpu_clk_BUFG                    |
| g1        | n/a   | BUFG/O          | None       |           1 |               0 |  0 |      0 |    0 |   0 |  0 |    0 |   1 |       0 | Clkgen/inst/clkfbout_buf_cpuclk |
| g2        | n/a   | BUFG/O          | None       |           0 |               1 |  0 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | Clkgen/inst/clk_out1            |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+---------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


10. Clock Region Cell Placement per Global Clock: Region X1Y0
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+--------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF  | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net          |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+--------------+
| g0        | n/a   | BUFG/O          | None       |         199 |               0 | 199 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | cpu_clk_BUFG |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+--------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


11. Clock Region Cell Placement per Global Clock: Region X0Y1
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net          |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------+
| g0        | n/a   | BUFG/O          | None       |          25 |               0 | 25 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | cpu_clk_BUFG |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


12. Clock Region Cell Placement per Global Clock: Region X1Y1
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net          |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------+
| g0        | n/a   | BUFG/O          | None       |          12 |               0 | 12 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | cpu_clk_BUFG |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+--------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts



# Location of BUFG Primitives 
set_property LOC BUFGCTRL_X0Y0 [get_cells cpu_clk_BUFG_inst]
set_property LOC BUFGCTRL_X0Y1 [get_cells Clkgen/inst/clkf_buf]
set_property LOC BUFGCTRL_X0Y2 [get_cells Clkgen/inst/clkout1_buf]

# Location of IO Primitives which is load of clock spine

# Location of clock ports
set_property LOC IOB_X0Y26 [get_ports fpga_clk]

# Clock net "cpu_clk_BUFG" driven by instance "cpu_clk_BUFG_inst" located at site "BUFGCTRL_X0Y0"
#startgroup
create_pblock {CLKAG_cpu_clk_BUFG}
add_cells_to_pblock [get_pblocks  {CLKAG_cpu_clk_BUFG}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="cpu_clk_BUFG"}]]]
resize_pblock [get_pblocks {CLKAG_cpu_clk_BUFG}] -add {CLOCKREGION_X0Y0:CLOCKREGION_X0Y0 CLOCKREGION_X0Y1:CLOCKREGION_X0Y1 CLOCKREGION_X1Y0:CLOCKREGION_X1Y0 CLOCKREGION_X1Y1:CLOCKREGION_X1Y1}
#endgroup

# Clock net "Clkgen/inst/clk_out1" driven by instance "Clkgen/inst/clkout1_buf" located at site "BUFGCTRL_X0Y2"
#startgroup
create_pblock {CLKAG_Clkgen/inst/clk_out1}
add_cells_to_pblock [get_pblocks  {CLKAG_Clkgen/inst/clk_out1}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="Clkgen/inst/clk_out1"}]]]
resize_pblock [get_pblocks {CLKAG_Clkgen/inst/clk_out1}] -add {CLOCKREGION_X0Y0:CLOCKREGION_X0Y0}
#endgroup
