<?xml version="1.0" encoding="UTF-8" ?>
<document>
<!--The data in this file is primarily intended for consumption by Xilinx tools.
The structure and the elements are likely to change over the next few releases.
This means code written to parse this file will need to be revisited each subsequent release.-->
<application name="pa" timeStamp="Thu Jul  3 13:39:17 2025">
<section name="Project Information" visible="false">
<property name="ProjectID" value="2be226096b024835a5e34908516e077e" type="ProjectID"/>
<property name="ProjectIteration" value="1" type="ProjectIteration"/>
</section>
<section name="PlanAhead Usage" visible="true">
<item name="Project Data">
<property name="SrcSetCount" value="1" type="SrcSetCount"/>
<property name="ConstraintSetCount" value="1" type="ConstraintSetCount"/>
<property name="DesignMode" value="RTL" type="DesignMode"/>
<property name="SynthesisStrategy" value="Vivado Synthesis Defaults" type="SynthesisStrategy"/>
<property name="ImplStrategy" value="Vivado Implementation Defaults" type="ImplStrategy"/>
</item>
<item name="Java Command Handlers">
<property name="ToolsSettings" value="1" type="JavaHandler"/>
</item>
<item name="Gui Handlers">
<property name="FileSetPanel_FILE_SET_PANEL_TREE" value="13" type="GuiHandlerData"/>
<property name="MainMenuMgr_TOOLS" value="2" type="GuiHandlerData"/>
<property name="PAViews_CODE" value="2" type="GuiHandlerData"/>
<property name="RDICommands_CUSTOM_COMMANDS" value="1" type="GuiHandlerData"/>
<property name="RDICommands_SETTINGS" value="1" type="GuiHandlerData"/>
</item>
<item name="Other">
<property name="GuiMode" value="2" type="GuiMode"/>
<property name="BatchMode" value="0" type="BatchMode"/>
<property name="TclMode" value="1" type="TclMode"/>
</item>
</section>
</application>
</document>
