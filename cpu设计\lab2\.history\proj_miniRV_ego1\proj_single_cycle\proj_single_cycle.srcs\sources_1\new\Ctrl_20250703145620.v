`timescale 1ns / 1ps

`include "defines.vh"

// Ctrl模块 - 控制单元
// 功能：根据指令的opcode、funct3、funct7生成控制信号
module Ctrl (
    input  wire [6:0]   opcode,     // 操作码
    input  wire [2:0]   funct3,     // 功能码3
    input  wire [6:0]   funct7,     // 功能码7
    input  wire [31:0]  alu_result, // ALU结果（用于分支判断）
    output reg  [1:0]   npc_op,     // NPC操作选择
    output reg          rf_we,      // 寄存器写使能
    output reg  [1:0]   rf_wsel,    // 写回数据选择
    output reg  [2:0]   sext_op,    // 立即数类型选择
    output reg  [3:0]   alu_op,     // ALU操作码
    output reg          alu_src,    // ALU第二个操作数选择
    output reg          mem_we      // 内存写使能
);

    // 控制信号生成逻辑
    always @(*) begin
        // 默认值
        npc_op   = `NPC_PC4;
        rf_we    = 1'b0;
        rf_wsel  = `WB_ALU;
        sext_op  = `IMM_I;
        alu_op   = `ALU_ADD;
        alu_src  = 1'b0;
        mem_we   = 1'b0;
        
        case (opcode)
            `OP_LUI: begin              // lui rd, imm
                rf_we    = 1'b1;
                rf_wsel  = `WB_IMM;
                sext_op  = `IMM_U;
            end
            
            `OP_JAL: begin              // jal rd, imm
                npc_op   = `NPC_JAL;
                rf_we    = 1'b1;
                rf_wsel  = `WB_PC4;
                sext_op  = `IMM_J;
            end
            
            `OP_JALR: begin             // jalr rd, rs1, imm
                npc_op   = `NPC_JUMP;
                rf_we    = 1'b1;
                rf_wsel  = `WB_PC4;
                sext_op  = `IMM_I;
                alu_op   = `ALU_ADD;
                alu_src  = 1'b1;
            end
            
            `OP_BRANCH: begin           // beq, blt
                sext_op  = `IMM_B;
                case (funct3)
                    3'b000: begin       // beq
                        alu_op = `ALU_SUB;
                        if (alu_result == 32'h0)
                            npc_op = `NPC_BRANCH;
                        else
                            npc_op = `NPC_PC4;
                    end
                    3'b100: begin       // blt
                        alu_op = `ALU_SLT;
                        if (alu_result[0] == 1'b1)
                            npc_op = `NPC_BRANCH;
                        else
                            npc_op = `NPC_PC4;
                    end
                    default: begin
                        alu_op = `ALU_SUB;
                        npc_op = `NPC_PC4;
                    end
                endcase
            end
            
            `OP_LOAD: begin             // lw rd, imm(rs1)
                rf_we    = 1'b1;
                rf_wsel  = `WB_MEM;
                sext_op  = `IMM_I;
                alu_op   = `ALU_ADD;
                alu_src  = 1'b1;
            end
            
            `OP_STORE: begin            // sw rs2, imm(rs1)
                sext_op  = `IMM_S;
                alu_op   = `ALU_ADD;
                alu_src  = 1'b1;
                mem_we   = 1'b1;
            end
            
            `OP_IMM: begin              // 立即数运算指令
                rf_we    = 1'b1;
                sext_op  = `IMM_I;
                alu_src  = 1'b1;
                case (funct3)
                    3'b000: alu_op = `ALU_ADD;      // addi
                    3'b111: alu_op = `ALU_AND;      // andi
                    3'b110: alu_op = `ALU_OR;       // ori
                    3'b100: alu_op = `ALU_XOR;      // xori
                    3'b001: alu_op = `ALU_SLL;      // slli
                    3'b101: begin
                        if (funct7[5] == 1'b0)
                            alu_op = `ALU_SRL;      // srli
                        else
                            alu_op = `ALU_SRA;      // srai
                    end
                    default: alu_op = `ALU_ADD;
                endcase
            end
            
            `OP_REG: begin              // 寄存器运算指令
                rf_we    = 1'b1;
                case (funct3)
                    3'b000: begin
                        if (funct7[5] == 1'b0)
                            alu_op = `ALU_ADD;      // add
                        else
                            alu_op = `ALU_SUB;      // sub (选做)
                    end
                    3'b111: alu_op = `ALU_AND;      // and
                    3'b110: alu_op = `ALU_OR;       // or
                    3'b100: alu_op = `ALU_XOR;      // xor
                    3'b001: alu_op = `ALU_SLL;      // sll
                    3'b101: begin
                        if (funct7[5] == 1'b0)
                            alu_op = `ALU_SRL;      // srl
                        else
                            alu_op = `ALU_SRA;      // sra
                    end
                    default: alu_op = `ALU_ADD;
                endcase
            end
            
            default: begin
                // 保持默认值
            end
        endcase
    end

endmodule
