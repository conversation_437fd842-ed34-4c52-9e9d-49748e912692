#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Thu Jul  3 14:36:02 2025
# Process ID: 4464
# Current directory: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/DRAM_synth_1
# Command line: vivado.exe -log DRAM.vds -product Vivado -mode batch -messageDb vivado.pb -notrace -source DRAM.tcl
# Log file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/DRAM_synth_1/DRAM.vds
# Journal file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/DRAM_synth_1\vivado.jou
#-----------------------------------------------------------
source DRAM.tcl -notrace
Command: synth_design -top DRAM -part xc7a35tcsg324-1 -mode out_of_context
Starting synth_design
Attempting to get a license for feature 'Synthesis' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Synthesis' and/or device 'xc7a35t'
INFO: Launching helper process for spawning children vivado processes
INFO: Helper process launched with PID 18040 
---------------------------------------------------------------------------------
Starting RTL Elaboration : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 466.727 ; gain = 99.746
---------------------------------------------------------------------------------
INFO: [Synth 8-638] synthesizing module 'DRAM' [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/synth/DRAM.vhd:69]
	Parameter C_FAMILY bound to: artix7 - type: string 
	Parameter C_ADDR_WIDTH bound to: 14 - type: integer 
	Parameter C_DEFAULT_DATA bound to: 0 - type: string 
	Parameter C_DEPTH bound to: 16384 - type: integer 
	Parameter C_HAS_CLK bound to: 1 - type: integer 
	Parameter C_HAS_D bound to: 1 - type: integer 
	Parameter C_HAS_DPO bound to: 0 - type: integer 
	Parameter C_HAS_DPRA bound to: 0 - type: integer 
	Parameter C_HAS_I_CE bound to: 0 - type: integer 
	Parameter C_HAS_QDPO bound to: 0 - type: integer 
	Parameter C_HAS_QDPO_CE bound to: 0 - type: integer 
	Parameter C_HAS_QDPO_CLK bound to: 0 - type: integer 
	Parameter C_HAS_QDPO_RST bound to: 0 - type: integer 
	Parameter C_HAS_QDPO_SRST bound to: 0 - type: integer 
	Parameter C_HAS_QSPO bound to: 0 - type: integer 
	Parameter C_HAS_QSPO_CE bound to: 0 - type: integer 
	Parameter C_HAS_QSPO_RST bound to: 0 - type: integer 
	Parameter C_HAS_QSPO_SRST bound to: 0 - type: integer 
	Parameter C_HAS_SPO bound to: 1 - type: integer 
	Parameter C_HAS_WE bound to: 1 - type: integer 
	Parameter C_MEM_INIT_FILE bound to: no_coe_file_loaded - type: string 
	Parameter C_ELABORATION_DIR bound to: ./ - type: string 
	Parameter C_MEM_TYPE bound to: 1 - type: integer 
	Parameter C_PIPELINE_STAGES bound to: 0 - type: integer 
	Parameter C_QCE_JOINED bound to: 0 - type: integer 
	Parameter C_QUALIFY_WE bound to: 0 - type: integer 
	Parameter C_READ_MIF bound to: 0 - type: integer 
	Parameter C_REG_A_D_INPUTS bound to: 0 - type: integer 
	Parameter C_REG_DPRA_INPUT bound to: 0 - type: integer 
	Parameter C_SYNC_ENABLE bound to: 1 - type: integer 
	Parameter C_WIDTH bound to: 32 - type: integer 
	Parameter C_PARSER_TYPE bound to: 1 - type: integer 
INFO: [Synth 8-3491] module 'dist_mem_gen_v8_0_12' declared at 'c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/hdl/dist_mem_gen_v8_0_vhsyn_rfs.vhd:3237' bound to instance 'U0' of component 'dist_mem_gen_v8_0_12' [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/synth/DRAM.vhd:135]
INFO: [Synth 8-256] done synthesizing module 'DRAM' (4#1) [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/synth/DRAM.vhd:69]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[31]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[30]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[29]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[28]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[27]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[26]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[25]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[24]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[23]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[22]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[21]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[20]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[19]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[18]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[17]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[16]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[15]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[14]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[13]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[12]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[11]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[10]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[9]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[8]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[7]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[6]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[5]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[4]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[3]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[2]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[1]
WARNING: [Synth 8-3331] design spram has unconnected port qspo[0]
WARNING: [Synth 8-3331] design spram has unconnected port i_ce
WARNING: [Synth 8-3331] design spram has unconnected port qspo_ce
WARNING: [Synth 8-3331] design spram has unconnected port qspo_rst
WARNING: [Synth 8-3331] design spram has unconnected port qspo_srst
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[31]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[30]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[29]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[28]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[27]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[26]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[25]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[24]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[23]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[22]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[21]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[20]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[19]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[18]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[17]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[16]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[15]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[14]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[13]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[12]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[11]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[10]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[9]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[8]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[7]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[6]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[5]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[4]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[3]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[2]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[1]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[0]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[31]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[30]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[29]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[28]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[27]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[26]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[25]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[24]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[23]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[22]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[21]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[20]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[19]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[18]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[17]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[16]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[15]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[14]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[13]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[12]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[11]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[10]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[9]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[8]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[7]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[6]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[5]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[4]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[3]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[2]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[1]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[0]
INFO: [Common 17-14] Message 'Synth 8-3331' appears 100 times and further instances of the messages will be disabled. Use the Tcl command set_msg_config to change the current settings.
---------------------------------------------------------------------------------
Finished RTL Elaboration : Time (s): cpu = 00:00:09 ; elapsed = 00:00:10 . Memory (MB): peak = 614.465 ; gain = 247.484
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:10 ; elapsed = 00:00:10 . Memory (MB): peak = 614.465 ; gain = 247.484
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 1 : Time (s): cpu = 00:00:10 ; elapsed = 00:00:10 . Memory (MB): peak = 614.465 ; gain = 247.484
---------------------------------------------------------------------------------
INFO: [Device 21-403] Loading part xc7a35tcsg324-1
INFO: [Project 1-570] Preparing netlist for logic optimization

Processing XDC Constraints
Initializing timing engine
Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM_ooc.xdc] for cell 'U0'
Finished Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM_ooc.xdc] for cell 'U0'
Parsing XDC File [C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/DRAM_synth_1/dont_touch.xdc]
Finished Parsing XDC File [C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/DRAM_synth_1/dont_touch.xdc]
Completed Processing XDC Constraints

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 819.648 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 819.648 ; gain = 0.000
Constraint Validation Runtime : Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.014 . Memory (MB): peak = 821.004 ; gain = 1.355
---------------------------------------------------------------------------------
Finished Constraint Validation : Time (s): cpu = 00:00:16 ; elapsed = 00:00:18 . Memory (MB): peak = 821.004 ; gain = 454.023
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Loading Part and Timing Information
---------------------------------------------------------------------------------
Loading part: xc7a35tcsg324-1
---------------------------------------------------------------------------------
Finished Loading Part and Timing Information : Time (s): cpu = 00:00:16 ; elapsed = 00:00:18 . Memory (MB): peak = 821.004 ; gain = 454.023
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Applying 'set_property' XDC Constraints
---------------------------------------------------------------------------------
Applied set_property DONT_TOUCH = true for U0. (constraint file  C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/DRAM_synth_1/dont_touch.xdc, line 9).
---------------------------------------------------------------------------------
Finished applying 'set_property' XDC Constraints : Time (s): cpu = 00:00:16 ; elapsed = 00:00:18 . Memory (MB): peak = 821.004 ; gain = 454.023
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 2 : Time (s): cpu = 00:00:17 ; elapsed = 00:00:18 . Memory (MB): peak = 821.004 ; gain = 454.023
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start RTL Component Statistics 
---------------------------------------------------------------------------------
Detailed RTL Component Info : 
+---Registers : 
	               32 Bit    Registers := 1     
---------------------------------------------------------------------------------
Finished RTL Component Statistics 
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start RTL Hierarchical Component Statistics 
---------------------------------------------------------------------------------
Hierarchical RTL Component report 
Module spram 
Detailed RTL Component Info : 
+---Registers : 
	               32 Bit    Registers := 1     
---------------------------------------------------------------------------------
Finished RTL Hierarchical Component Statistics
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Part Resource Summary
---------------------------------------------------------------------------------
Part Resources:
DSPs: 90 (col length:60)
BRAMs: 100 (col length: RAMB18 60 RAMB36 30)
---------------------------------------------------------------------------------
Finished Part Resource Summary
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Cross Boundary and Area Optimization
---------------------------------------------------------------------------------
Warning: Parallel synthesis criteria is not met 
---------------------------------------------------------------------------------
Finished Cross Boundary and Area Optimization : Time (s): cpu = 00:00:19 ; elapsed = 00:00:21 . Memory (MB): peak = 821.004 ; gain = 454.023
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Distributed RAM: Preliminary Mapping  Report (see note below)
+------------+-----------------------------------------------------------+----------------+----------------------+--------------------+
|Module Name | RTL Object                                                | Inference      | Size (Depth x Width) | Primitives         | 
+------------+-----------------------------------------------------------+----------------+----------------------+--------------------+
|U0          | synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg | User Attribute | 16 K x 32            | RAM256X1S x 2048   | 
+------------+-----------------------------------------------------------+----------------+----------------------+--------------------+

Note: The table above is a preliminary report that shows the Distributed RAMs at the current stage of the synthesis flow. Some Distributed RAMs may be reimplemented as non Distributed RAM primitives later in the synthesis flow. Multiple instantiated RAMs are reported only once.
---------------------------------------------------------------------------------
Finished ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Applying XDC Timing Constraints
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Applying XDC Timing Constraints : Time (s): cpu = 00:00:24 ; elapsed = 00:00:26 . Memory (MB): peak = 846.902 ; gain = 479.922
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Timing Optimization
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Timing Optimization : Time (s): cpu = 00:00:25 ; elapsed = 00:00:27 . Memory (MB): peak = 876.262 ; gain = 509.281
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Distributed RAM: Final Mapping  Report
+------------+-----------------------------------------------------------+----------------+----------------------+--------------------+
|Module Name | RTL Object                                                | Inference      | Size (Depth x Width) | Primitives         | 
+------------+-----------------------------------------------------------+----------------+----------------------+--------------------+
|U0          | synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg | User Attribute | 16 K x 32            | RAM256X1S x 2048   | 
+------------+-----------------------------------------------------------+----------------+----------------------+--------------------+

---------------------------------------------------------------------------------
Finished ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Technology Mapping
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Technology Mapping : Time (s): cpu = 00:00:25 ; elapsed = 00:00:27 . Memory (MB): peak = 894.699 ; gain = 527.719
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished IO Insertion : Time (s): cpu = 00:00:26 ; elapsed = 00:00:28 . Memory (MB): peak = 894.699 ; gain = 527.719
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Renaming Generated Instances
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Instances : Time (s): cpu = 00:00:26 ; elapsed = 00:00:28 . Memory (MB): peak = 894.699 ; gain = 527.719
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Rebuilding User Hierarchy
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Rebuilding User Hierarchy : Time (s): cpu = 00:00:26 ; elapsed = 00:00:28 . Memory (MB): peak = 894.699 ; gain = 527.719
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Ports
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Ports : Time (s): cpu = 00:00:26 ; elapsed = 00:00:28 . Memory (MB): peak = 894.699 ; gain = 527.719
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:26 ; elapsed = 00:00:28 . Memory (MB): peak = 894.699 ; gain = 527.719
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Nets
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Nets : Time (s): cpu = 00:00:26 ; elapsed = 00:00:28 . Memory (MB): peak = 894.699 ; gain = 527.719
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Writing Synthesis Report
---------------------------------------------------------------------------------

Report BlackBoxes: 
+-+--------------+----------+
| |BlackBox name |Instances |
+-+--------------+----------+
+-+--------------+----------+

Report Cell Usage: 
+------+----------+------+
|      |Cell      |Count |
+------+----------+------+
|1     |LUT2      |    13|
|2     |LUT3      |     2|
|3     |LUT5      |     2|
|4     |LUT6      |   606|
|5     |MUXF7     |   256|
|6     |MUXF8     |   128|
|7     |RAM256X1S |  2048|
|8     |FDRE      |    32|
+------+----------+------+

Report Instance Areas: 
+------+----------------------------------+---------------------------+------+
|      |Instance                          |Module                     |Cells |
+------+----------------------------------+---------------------------+------+
|1     |top                               |                           |  3087|
|2     |  U0                              |dist_mem_gen_v8_0_12       |  3087|
|3     |    \synth_options.dist_mem_inst  |dist_mem_gen_v8_0_12_synth |  3087|
|4     |      \gen_sp_ram.spram_inst      |spram                      |  3087|
+------+----------------------------------+---------------------------+------+
---------------------------------------------------------------------------------
Finished Writing Synthesis Report : Time (s): cpu = 00:00:26 ; elapsed = 00:00:28 . Memory (MB): peak = 894.699 ; gain = 527.719
---------------------------------------------------------------------------------
Synthesis finished with 0 errors, 0 critical warnings and 118 warnings.
Synthesis Optimization Runtime : Time (s): cpu = 00:00:12 ; elapsed = 00:00:22 . Memory (MB): peak = 894.699 ; gain = 321.180
Synthesis Optimization Complete : Time (s): cpu = 00:00:26 ; elapsed = 00:00:28 . Memory (MB): peak = 894.699 ; gain = 527.719
INFO: [Project 1-571] Translating synthesized netlist
INFO: [Netlist 29-17] Analyzing 2432 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
WARNING: [Netlist 29-101] Netlist 'DRAM' is not ideal for floorplanning, since the cellview 'spram' contains a large number of primitives.  Please consider enabling hierarchy in synthesis if you want to do floorplanning.
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 904.492 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
  A total of 2048 instances were transformed.
  RAM256X1S => RAM256X1S (MUXF7, MUXF7, MUXF8, RAMS64E, RAMS64E, RAMS64E, RAMS64E): 2048 instances

INFO: [Common 17-83] Releasing license: Synthesis
15 Infos, 101 Warnings, 0 Critical Warnings and 0 Errors encountered.
synth_design completed successfully
synth_design: Time (s): cpu = 00:00:27 ; elapsed = 00:00:30 . Memory (MB): peak = 904.492 ; gain = 548.992
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 904.492 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/DRAM_synth_1/DRAM.dcp' has been generated.
INFO: [Coretcl 2-1648] Added synthesis output to IP cache for IP DRAM, cache-ID = 63d0c76c10e9b1cd
INFO: [Coretcl 2-1174] Renamed 3 cell refs.
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 908.918 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/DRAM_synth_1/DRAM.dcp' has been generated.
INFO: [runtcl-4] Executing : report_utilization -file DRAM_utilization_synth.rpt -pb DRAM_utilization_synth.pb
INFO: [Common 17-206] Exiting Vivado at Thu Jul  3 14:36:38 2025...
