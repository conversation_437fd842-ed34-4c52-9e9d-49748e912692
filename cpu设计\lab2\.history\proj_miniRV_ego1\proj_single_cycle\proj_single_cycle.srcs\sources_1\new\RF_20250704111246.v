`timescale 1ns / 1ps

module RF (
    input  wire         clk,       
    input  wire [4:0]   rR1,        
    output wire [31:0]  rD1,        
    input  wire [4:0]   rR2,        
    output wire [31:0]  rD2, 
    input  wire         we,        
    input  wire [4:0]   wR,         
    input  wire [31:0]  wD      
);

    // 32个32位寄存器
    reg [31:0] registers [31:0];

    // 异步读操作
    assign rD1 = (rR1 == 5'b0) ? 32'h0 : registers[rR1];
    assign rD2 = (rR2 == 5'b0) ? 32'h0 : registers[rR2];

    // 同步写操作
    always @(posedge clk) begin
        if (we && wR != 5'b0) begin     // 写使能且不是x0寄存器
            registers[wR] <= wD;
        end
    end

endmodule