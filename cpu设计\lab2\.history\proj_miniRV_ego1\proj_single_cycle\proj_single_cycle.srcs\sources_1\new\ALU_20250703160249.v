`timescale 1ns / 1ps

`include "defines.vh"

// TODO: ALU模块 - 算术逻辑运算单元
// 功能：完成算术、逻辑、移位和比较运算
module ALU (
    input  wire [31:0]  A,          // 操作数A
    input  wire [31:0]  B,          // 操作数B
    input  wire [3:0]   alu_op,     // ALU操作码
    output reg  [31:0]  C           // 运算结果
);

    // TODO: 根据alu_op执行相应运算
    always @(*) begin
        case (alu_op)
            `ALU_ADD:  C = A + B;                           // 加法
            `ALU_SUB:  C = A - B;                           // 减法
            `ALU_AND:  C = A & B;                           // 按位与
            `ALU_OR:   C = A | B;                           // 按位或
            `ALU_XOR:  C = A ^ B;                           // 按位异或
            `ALU_SLL:  C = A << B[4:0];                     // 逻辑左移
            `ALU_SRL:  C = A >> B[4:0];                     // 逻辑右移
            `ALU_SRA:  C = $signed(A) >>> B[4:0];           // 算术右移
            `ALU_SLT:  C = ($signed(A) < $signed(B)) ? 32'h1 : 32'h0;  // 有符号比较
            `ALU_SLTU: C = (A < B) ? 32'h1 : 32'h0;         // 无符号比较
            default:   C = 32'h0;                           // 默认输出0
        endcase
    end

endmodule
