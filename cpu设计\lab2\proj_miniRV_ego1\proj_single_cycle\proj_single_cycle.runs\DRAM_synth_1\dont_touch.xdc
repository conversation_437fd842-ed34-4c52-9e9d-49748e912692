# This file is automatically generated.
# It contains project source information necessary for synthesis and implementation.

# IP: c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci
# IP: The module: 'DRAM' is the root of the design. Do not add the DONT_TOUCH constraint.

# XDC: c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM_ooc.xdc
# XDC: The top module name and the constraint reference have the same name: 'DRAM'. Do not add the DONT_TOUCH constraint.
set_property DONT_TOUCH TRUE [get_cells U0 -quiet] -quiet

# IP: c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci
# IP: The module: 'DRAM' is the root of the design. Do not add the DONT_TOUCH constraint.

# XDC: c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM_ooc.xdc
# XDC: The top module name and the constraint reference have the same name: 'DRAM'. Do not add the DONT_TOUCH constraint.
#dup# set_property DONT_TOUCH TRUE [get_cells U0 -quiet] -quiet
