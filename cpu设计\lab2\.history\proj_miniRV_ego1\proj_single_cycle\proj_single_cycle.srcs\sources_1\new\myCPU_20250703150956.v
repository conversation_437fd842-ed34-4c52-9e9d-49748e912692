`timescale 1ns / 1ps

`include "defines.vh"

module myCPU (
    input  wire         cpu_rst,
    input  wire         cpu_clk,

    // Interface to IROM
`ifdef RUN_TRACE
    output wire [15:0]  inst_addr,
`else
    output wire [13:0]  inst_addr,
`endif
    input  wire [31:0]  inst,
    
    // Interface to Bridge
    output wire [31:0]  Bus_addr,
    input  wire [31:0]  Bus_rdata,
    output wire         Bus_we,
    output wire [31:0]  Bus_wdata

`ifdef RUN_TRACE
    ,// Debug Interface
    output wire         debug_wb_have_inst,
    output wire [31:0]  debug_wb_pc,
    output              debug_wb_ena,
    output wire [ 4:0]  debug_wb_reg,
    output wire [31:0]  debug_wb_value
`endif
);

    // TODO: 完成你自己的单周期CPU设计
    // 内部信号定义
    wire [31:0] pc_current;         // 当前PC值
    wire [31:0] pc_next;            // 下一个PC值
    wire [31:0] pc_plus4;           // PC + 4

    // 指令字段
    wire [6:0]  opcode;             // 操作码
    wire [4:0]  rs1;                // 源寄存器1
    wire [4:0]  rs2;                // 源寄存器2
    wire [4:0]  rd;                 // 目标寄存器
    wire [2:0]  funct3;             // 功能码3
    wire [6:0]  funct7;             // 功能码7

    // 寄存器堆信号
    wire [31:0] rf_rD1;             // 寄存器堆读数据1
    wire [31:0] rf_rD2;             // 寄存器堆读数据2
    wire [31:0] rf_wD;              // 寄存器堆写数据
    wire        rf_we;              // 寄存器堆写使能
    wire [1:0]  rf_wsel;            // 写回数据选择

    // ALU信号
    wire [31:0] alu_A;              // ALU操作数A
    wire [31:0] alu_B;              // ALU操作数B
    wire [31:0] alu_C;              // ALU结果
    wire [3:0]  alu_op;             // ALU操作码
    wire        alu_src;            // ALU第二个操作数选择

    // 立即数扩展信号
    wire [31:0] sext_ext;           // 扩展后的立即数
    wire [2:0]  sext_op;            // 立即数类型选择

    // NPC信号
    wire [1:0]  npc_op;             // NPC操作选择

    // 内存信号
    wire        mem_we;             // 内存写使能

    // TODO: 指令字段解析
    assign opcode = inst[6:0];
    assign rs1    = inst[19:15];
    assign rs2    = inst[24:20];
    assign rd     = inst[11:7];
    assign funct3 = inst[14:12];
    assign funct7 = inst[31:25];

    // TODO: PC相关信号
    assign pc_plus4 = pc_current + 4;

    // TODO: 指令地址输出
`ifdef RUN_TRACE
    assign inst_addr = pc_current[15:0];
`else
    assign inst_addr = pc_current[15:2];
`endif

    // TODO: ALU操作数选择
    assign alu_A = rf_rD1;
    assign alu_B = alu_src ? sext_ext : rf_rD2;

    // TODO: 写回数据选择
    always @(*) begin
        case (rf_wsel)
            `WB_ALU:  rf_wD = alu_C;
            `WB_MEM:  rf_wD = Bus_rdata;
            `WB_PC4:  rf_wD = pc_plus4;
            `WB_IMM:  rf_wD = sext_ext;
            default:  rf_wD = alu_C;
        endcase
    end

    // TODO: 总线接口
    assign Bus_addr  = alu_C;
    assign Bus_wdata = rf_rD2;
    assign Bus_we    = mem_we;
    //

`ifdef RUN_TRACE
    // Debug Interface
    assign debug_wb_have_inst = /* TODO */;
    assign debug_wb_pc        = /* TODO */;
    assign debug_wb_ena       = /* TODO */;
    assign debug_wb_reg       = /* TODO */;
    assign debug_wb_value     = /* TODO */;
`endif

endmodule
