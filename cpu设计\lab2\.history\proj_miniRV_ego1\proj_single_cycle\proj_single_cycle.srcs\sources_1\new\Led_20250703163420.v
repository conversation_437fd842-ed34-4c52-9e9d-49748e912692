`timescale 1ns / 1ps

`include "defines.vh"

// TODO: LED接口模块
// 功能：控制16位LED灯的亮灭
module Led (
    input  wire         rst,        // 复位信号
    input  wire         clk,        // 时钟信号
    input  wire [31:0]  addr,       // 地址信号
    input  wire         we,         // 写使能信号
    input  wire [31:0]  wdata,      // 写数据信号

    output reg  [15:0]  led         // LED硬件接口
);

    // TODO: LED数据寄存器
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            led <= 16'h0;
        end else if (we && addr == `PERI_ADDR_LED) begin
            led <= wdata[15:0];  // 只使用低16位
        end
    end

endmodule
