Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
-----------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Fri Jul  4 10:23:48 2025
| Host         : L running 64-bit major release  (build 9200)
| Command      : report_utilization -file miniRV_SoC_utilization_placed.rpt -pb miniRV_SoC_utilization_placed.pb
| Design       : miniRV_SoC
| Device       : 7a35tcsg324-1
| Design State : Fully Placed
-----------------------------------------------------------------------------------------------------------------

Utilization Design Information

Table of Contents
-----------------
1. Slice Logic
1.1 Summary of Registers by Type
2. Slice Logic Distribution
3. Memory
4. DSP
5. IO and GT Specific
6. Clocking
7. Specific Feature
8. Primitives
9. Black Boxes
10. Instantiated Netlists

1. Slice Logic
--------------

+----------------------------+-------+-------+-----------+-------+
|          Site Type         |  Used | Fixed | Available | Util% |
+----------------------------+-------+-------+-----------+-------+
| Slice LUTs                 | 10099 |     0 |     20800 | 48.55 |
|   LUT as Logic             |  1859 |     0 |     20800 |  8.94 |
|   LUT as Memory            |  8240 |     0 |      9600 | 85.83 |
|     LUT as Distributed RAM |  8240 |     0 |           |       |
|     LUT as Shift Register  |     0 |     0 |           |       |
| Slice Registers            |   310 |     0 |     41600 |  0.75 |
|   Register as Flip Flop    |   310 |     0 |     41600 |  0.75 |
|   Register as Latch        |     0 |     0 |     41600 |  0.00 |
| F7 Muxes                   |  4356 |     0 |     16300 | 26.72 |
| F8 Muxes                   |  2176 |     0 |      8150 | 26.70 |
+----------------------------+-------+-------+-----------+-------+


1.1 Summary of Registers by Type
--------------------------------

+-------+--------------+-------------+--------------+
| Total | Clock Enable | Synchronous | Asynchronous |
+-------+--------------+-------------+--------------+
| 0     |            _ |           - |            - |
| 0     |            _ |           - |          Set |
| 0     |            _ |           - |        Reset |
| 0     |            _ |         Set |            - |
| 0     |            _ |       Reset |            - |
| 0     |          Yes |           - |            - |
| 12    |          Yes |           - |          Set |
| 298   |          Yes |           - |        Reset |
| 0     |          Yes |         Set |            - |
| 0     |          Yes |       Reset |            - |
+-------+--------------+-------------+--------------+


2. Slice Logic Distribution
---------------------------

+--------------------------------------------+------+-------+-----------+-------+
|                  Site Type                 | Used | Fixed | Available | Util% |
+--------------------------------------------+------+-------+-----------+-------+
| Slice                                      | 2704 |     0 |      8150 | 33.18 |
|   SLICEL                                   |  644 |     0 |           |       |
|   SLICEM                                   | 2060 |     0 |           |       |
| LUT as Logic                               | 1859 |     0 |     20800 |  8.94 |
|   using O5 output only                     |    0 |       |           |       |
|   using O6 output only                     | 1674 |       |           |       |
|   using O5 and O6                          |  185 |       |           |       |
| LUT as Memory                              | 8240 |     0 |      9600 | 85.83 |
|   LUT as Distributed RAM                   | 8240 |     0 |           |       |
|     using O5 output only                   |    0 |       |           |       |
|     using O6 output only                   | 8192 |       |           |       |
|     using O5 and O6                        |   48 |       |           |       |
|   LUT as Shift Register                    |    0 |     0 |           |       |
| Slice Registers                            |  310 |     0 |     41600 |  0.75 |
|   Register driven from within the Slice    |  217 |       |           |       |
|   Register driven from outside the Slice   |   93 |       |           |       |
|     LUT in front of the register is unused |   59 |       |           |       |
|     LUT in front of the register is used   |   34 |       |           |       |
| Unique Control Sets                        |   74 |       |      8150 |  0.91 |
+--------------------------------------------+------+-------+-----------+-------+
* Note: Available Control Sets calculated as Slice Registers / 8, Review the Control Sets Report for more information regarding control sets.


3. Memory
---------

+----------------+------+-------+-----------+-------+
|    Site Type   | Used | Fixed | Available | Util% |
+----------------+------+-------+-----------+-------+
| Block RAM Tile |    0 |     0 |        50 |  0.00 |
|   RAMB36/FIFO* |    0 |     0 |        50 |  0.00 |
|   RAMB18       |    0 |     0 |       100 |  0.00 |
+----------------+------+-------+-----------+-------+
* Note: Each Block RAM Tile only has one FIFO logic available and therefore can accommodate only one FIFO36E1 or one FIFO18E1. However, if a FIFO18E1 occupies a Block RAM Tile, that tile can still accommodate a RAMB18E1


4. DSP
------

+-----------+------+-------+-----------+-------+
| Site Type | Used | Fixed | Available | Util% |
+-----------+------+-------+-----------+-------+
| DSPs      |    0 |     0 |        90 |  0.00 |
+-----------+------+-------+-----------+-------+


5. IO and GT Specific
---------------------

+-----------------------------+------+-------+-----------+-------+
|          Site Type          | Used | Fixed | Available | Util% |
+-----------------------------+------+-------+-----------+-------+
| Bonded IOB                  |   63 |    63 |       210 | 30.00 |
|   IOB Master Pads           |   30 |       |           |       |
|   IOB Slave Pads            |   30 |       |           |       |
| Bonded IPADs                |    0 |     0 |         2 |  0.00 |
| PHY_CONTROL                 |    0 |     0 |         5 |  0.00 |
| PHASER_REF                  |    0 |     0 |         5 |  0.00 |
| OUT_FIFO                    |    0 |     0 |        20 |  0.00 |
| IN_FIFO                     |    0 |     0 |        20 |  0.00 |
| IDELAYCTRL                  |    0 |     0 |         5 |  0.00 |
| IBUFDS                      |    0 |     0 |       202 |  0.00 |
| PHASER_OUT/PHASER_OUT_PHY   |    0 |     0 |        20 |  0.00 |
| PHASER_IN/PHASER_IN_PHY     |    0 |     0 |        20 |  0.00 |
| IDELAYE2/IDELAYE2_FINEDELAY |    0 |     0 |       250 |  0.00 |
| ILOGIC                      |    0 |     0 |       210 |  0.00 |
| OLOGIC                      |    0 |     0 |       210 |  0.00 |
+-----------------------------+------+-------+-----------+-------+


6. Clocking
-----------

+------------+------+-------+-----------+-------+
|  Site Type | Used | Fixed | Available | Util% |
+------------+------+-------+-----------+-------+
| BUFGCTRL   |    3 |     0 |        32 |  9.38 |
| BUFIO      |    0 |     0 |        20 |  0.00 |
| MMCME2_ADV |    0 |     0 |         5 |  0.00 |
| PLLE2_ADV  |    1 |     0 |         5 | 20.00 |
| BUFMRCE    |    0 |     0 |        10 |  0.00 |
| BUFHCE     |    0 |     0 |        72 |  0.00 |
| BUFR       |    0 |     0 |        20 |  0.00 |
+------------+------+-------+-----------+-------+


7. Specific Feature
-------------------

+-------------+------+-------+-----------+-------+
|  Site Type  | Used | Fixed | Available | Util% |
+-------------+------+-------+-----------+-------+
| BSCANE2     |    0 |     0 |         4 |  0.00 |
| CAPTUREE2   |    0 |     0 |         1 |  0.00 |
| DNA_PORT    |    0 |     0 |         1 |  0.00 |
| EFUSE_USR   |    0 |     0 |         1 |  0.00 |
| FRAME_ECCE2 |    0 |     0 |         1 |  0.00 |
| ICAPE2      |    0 |     0 |         2 |  0.00 |
| PCIE_2_1    |    0 |     0 |         1 |  0.00 |
| STARTUPE2   |    0 |     0 |         1 |  0.00 |
| XADC        |    0 |     0 |         1 |  0.00 |
+-------------+------+-------+-----------+-------+


8. Primitives
-------------

+-----------+------+---------------------+
|  Ref Name | Used | Functional Category |
+-----------+------+---------------------+
| RAMS64E   | 8192 |  Distributed Memory |
| MUXF7     | 4356 |               MuxFx |
| MUXF8     | 2176 |               MuxFx |
| LUT6      | 1328 |                 LUT |
| FDCE      |  298 |        Flop & Latch |
| LUT4      |  238 |                 LUT |
| LUT5      |  180 |                 LUT |
| LUT3      |  140 |                 LUT |
| LUT2      |  123 |                 LUT |
| CARRY4    |  100 |          CarryLogic |
| RAMD32    |   72 |  Distributed Memory |
| OBUF      |   40 |                  IO |
| LUT1      |   35 |                 LUT |
| RAMS32    |   24 |  Distributed Memory |
| IBUF      |   23 |                  IO |
| FDPE      |   12 |        Flop & Latch |
| BUFG      |    3 |               Clock |
| PLLE2_ADV |    1 |               Clock |
+-----------+------+---------------------+


9. Black Boxes
--------------

+----------+------+
| Ref Name | Used |
+----------+------+


10. Instantiated Netlists
-------------------------

+----------+------+
| Ref Name | Used |
+----------+------+
| cpuclk   |    1 |
| IROM     |    1 |
| DRAM     |    1 |
+----------+------+


